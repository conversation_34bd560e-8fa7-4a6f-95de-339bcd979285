name: node.js CI

on:
  push:
    branches:
      - "main"
      - "dev"
      - "prod"
  pull_request:
    branches:
      - "main"
      - "dev"
      - "prod"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18]

    steps:
      - uses: actions/checkout@v3
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: "yarn"

      - name: Generate Environment Variables File for Production
        env:
          REACT_APP_DOMAIN: ${{ secrets.your_key }}
        run: |
          echo "your_key=$your_key" >> .env

      - name: Install Dependencies
        run: yarn install

      - name: Build Packages
        run: yarn build
