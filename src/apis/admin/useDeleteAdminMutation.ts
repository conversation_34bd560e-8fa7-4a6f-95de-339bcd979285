import { useMutation, useQueryClient } from "@tanstack/react-query";
import { AuthInstance } from "..";

export const useDeleteAdminMutation = () => {
  const queryClient = useQueryClient();

  const fetcher = async (accountId: number) => {
    return await AuthInstance.delete(`/backoffice-pet/admin/${accountId}`, {
      params: {
        accountId,
      },
    });
  };

  return useMutation({
    mutationFn: fetcher,
    mutationKey: ["deleteAdmin"],
    onSuccess: (res) => {
      console.log(res);
      alert("삭제되었습니다. ");
      queryClient.prefetchQuery({ queryKey: ["allAccounts"] });
    },
    onError: (err) => {
      console.error(err);
      alert("에러가 발생하였습니다. 다시 시도해주세요.");
    },
  });
};
