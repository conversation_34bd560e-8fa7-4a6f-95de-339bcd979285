import { useQuery } from "@tanstack/react-query";
import { AuthInstance } from "..";
import { DetailAccountResponseType } from "@/models";

export const useDetailAccountQuery = (accountId: number) => {
  const fetcher = async () => {
    const { data } = await AuthInstance.get<DetailAccountResponseType>(
      `/backoffice-pet/admin/${accountId}`,
      {
        params: {
          accountId,
        },
      }
    );

    return data;
  };

  return useQuery<DetailAccountResponseType>({
    queryKey: ["detailAccount", accountId],
    queryFn: fetcher,
  });
};
