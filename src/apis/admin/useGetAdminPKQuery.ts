import { useQuery } from "@tanstack/react-query";
import { AuthInstance } from "..";
import { DetailAccountResponseType } from "@/models";

export const useGetAdminPKQuery = (accountId: number) => {
  const token = sessionStorage.getItem("auth");

  const fetcher = async () => {
    const { data } = await AuthInstance.get<DetailAccountResponseType>(
      `/backoffice-pet/admin/${accountId}`,
      {
        params: {
          accountId,
        },
      }
    );

    return data;
  };

  return useQuery<DetailAccountResponseType>({
    queryFn: fetcher,
    queryKey: ["adminPK", accountId],
    enabled: token !== null,
  });
};
