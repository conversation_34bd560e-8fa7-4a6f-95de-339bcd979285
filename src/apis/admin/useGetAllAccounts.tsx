import { AllAccountsRequestType, AllAccountsResponseType } from "@/models";
import { AuthInstance } from "..";
import { useQuery } from "@tanstack/react-query";

export const useGetAllAccountsQuery = (options: AllAccountsRequestType) => {
  const fetcher = async () => {
    const {
      page,
      limit,
      sort,
      order,
      status,
      accountId,
      countryNumber,
      phone,
      email,
      start,
      end,
    } = options;

    const params = {
      page,
      limit,
      sort,
      order,
      ...(status && { status }),
      ...(accountId && { accountId }),
      ...(countryNumber && { countryNumber }),
      ...(phone && { phone }),
      ...(email && { email }),
      ...(start && { start }),
      ...(end && { end }),
    };

    const { data } = await AuthInstance.get<AllAccountsResponseType>(
      "/backoffice-pet/admin",
      { params }
    );

    return data;
  };

  return useQuery<AllAccountsResponseType>({
    queryKey: ["allAccounts"],
    queryFn: fetcher,
  });
};
