import { useMutation, useQueryClient } from "@tanstack/react-query";
import { AuthInstance } from "..";
import { useAnimation, useSelectUser } from "@/hooks";

export const usePatchAdminMutation = (accountId: number) => {
  const queryClient = useQueryClient();
  const { handleClose } = useAnimation("userEdit");
  const [user] = useSelectUser();

  const fetcher = async (req: { password: string; name: string }) => {
    return await AuthInstance.patch(`/backoffice-pet/admin/${accountId}`, req, {
      params: {
        accountId,
      },
    });
  };

  return useMutation({
    mutationFn: fetcher,
    mutationKey: ["patchAdmin"],
    onSuccess: (res) => {
      console.log(res);
      alert("수정되었습니다. ");
      queryClient.prefetchQuery({ queryKey: ["allAccounts"] });
      queryClient.prefetchQuery({ queryKey: ["detailAccount", user?.id] });
      handleClose();
    },
    onError: (err) => {
      console.error(err);
      alert("에러가 발생하였습니다. 다시 시도해주세요.");
    },
  });
};
