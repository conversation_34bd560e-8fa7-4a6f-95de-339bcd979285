import { SuccessBoatOptionsType } from "@/models";
import { AuthInstance } from "../index";
import { useQuery } from "@tanstack/react-query";

export const useAllBoatsCsv = (params?: SuccessBoatOptionsType) => {
  const fetcher = async () => {
    const { data } = await AuthInstance.get(
      `/backoffice-pet/analysis/boats/csv`,
      {
        params,
      }
    );

    return data;
  };

  return useQuery({
    queryFn: fetcher,
    queryKey: ["allBoatCsv"],
    enabled: false,
  });
};
