import { GetSuccessBoatRequestType, GetSuccessBoatResponseType } from "@/models/analysis";
import { useQuery } from "@tanstack/react-query";
import { AuthInstance } from "../index";

export const useGetAnalysisBoatQuery = (options: GetSuccessBoatRequestType) => {
  const fetcher = async () => {
    const { limit, page, sort, order, start, end, boatId, subjectId, blood, protein, glucose, ph, ketone } = options;
    const params = {
      limit,
      page,
      sort,
      order,
      start,
      end,
      boatId,
      subjectId,
      blood,
      protein,
      glucose,
      ph,
      ketone,
    };
    const { data } = await AuthInstance.get<GetSuccessBoatResponseType>(
      `/backoffice-pet/analysis/boats`,
      {
        params,
      }
    );
    return data;
  };

  return useQuery<GetSuccessBoatResponseType>({
    queryKey: ["successAnalysis"],
    queryFn: fetcher,
  });
};
