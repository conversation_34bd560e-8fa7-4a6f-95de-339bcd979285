import { useQuery } from "@tanstack/react-query";
import { AuthInstance } from "../index";
import { GetErrorDataRequestType, GetErrorDataResponseType } from "@/models";

export const useGetErrorBoatQuery = (
  options: GetErrorDataRequestType,
  sw: "on" | "off"
) => {
  const fetcher = async () => {
    const {
      limit,
      page,
      sort,
      order,
      start,
      end,
      type,
      analysisErrorId,
      subjectId,
    } = options;
    const params = {
      limit,
      page,
      sort,
      order,
      ...(start && { start }),
      ...(end && { end }),
      ...(type && { type }),
      ...(analysisErrorId && { analysisErrorId }),
      ...(subjectId && { subjectId }),
    };
    
    const { data } = await AuthInstance.get<GetErrorDataResponseType>(
      `/backoffice-pet/analysis/boats/${sw === "on" ? "crop" : "original"}`,
      {
        params,
      }
    );

    return data;
  };

  return useQuery<GetErrorDataResponseType>({
    queryKey: ["errorBoat"],
    queryFn: fetcher,
  });
};
