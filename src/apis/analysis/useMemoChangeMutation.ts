import { useMutation, useQueryClient } from "@tanstack/react-query";
import { AuthInstance } from "../index";
import { useAnimation } from "@/hooks";

export const useMemoChangeMutation = (memo: string, boatId: number) => {
  const { handleClose } = useAnimation("analysisSuccess");
  const queryClient = useQueryClient();

  const fetcher = async () => {
    return AuthInstance.patch(`/backoffice-pet/analysis/boats/${boatId}/memo`, {
      memo,
    });
  };

  return useMutation({
    mutationKey: ["memoChange"],
    mutationFn: fetcher,
    onSuccess: (res) => {
      console.log(res);
      alert("성공적으로 메모가 변경되었습니다.");
      handleClose();
      queryClient.prefetchQuery({ queryKey: ["successAnalysis"] });
    },
    onError: (err) => {
      console.error(err);
      alert("메모 변경에 실패하였습니다. 다시 시도해주세요.");
    },
  });
};
