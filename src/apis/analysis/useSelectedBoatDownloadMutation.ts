import { useMutation } from "@tanstack/react-query";
import { AuthInstance } from "../index";
import { useCsv } from "@/hooks";

export const useSelectedBoatDownloadMutation = (boatIds: number[]) => {
  const [downloadCsv] = useCsv();

  const fetcher = async () => {
    const { data } = await AuthInstance.post<string>(
      `/backoffice-pet/analysis/boats/csv`,
      {
        boatIds,
      }
    );

    return data;
  };

  return useMutation<string>({
    mutationFn: fetcher,
    mutationKey: ["selectedBoatDownload"],
    onSuccess: (res) => {
      console.log(res);
      downloadCsv(res, "filter-result-csv-file", "csv");
    },
    onError: (err) => {
      console.error(err);
      alert("다운로드에 실패하였습니다. 다시 시도해주세요.");
    },
  });
};
