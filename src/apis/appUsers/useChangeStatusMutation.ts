import { useMutation, useQueryClient } from "@tanstack/react-query";
import { AuthInstance } from "../index";
import { useAnimation } from "@/hooks";

type ChangeStatusMutationVariables = "Active" | "Archived";

export const useChangeStatusMutation = (accountId: number, status: ChangeStatusMutationVariables) => {
  const queryClient = useQueryClient();
  const { handleClose } = useAnimation("appUser");

  const fetcher = async () => {
    return await AuthInstance.patch(`/backoffice-pet/accounts/${accountId}`, {
      status: status.toLowerCase(),
    });
  };

  return useMutation({
    mutationFn: fetcher,
    onSuccess: (res) => {
      console.log(res);
      alert("성공적으로 변경되었습니다.");
      handleClose();
      queryClient.prefetchQuery({ queryKey: ["getAppUsers"] });
    },
    onError: (err) => {
      console.error(err);
    },
  });
};
