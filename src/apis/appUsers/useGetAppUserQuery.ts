import { GetAppUserQueryRequestType, GetAppUserResponseType } from "@/models";
import { AuthInstance } from "../index";
import { useQuery } from "@tanstack/react-query";

export const useGetAppUserQuery = (options: GetAppUserQueryRequestType) => {
  const fetcher = async () => {
    const {
      page,
      limit,
      sort,
      order,
      status,
      accountId,
      generalId,
      accountType,
      countryNumber,
      nickname,
      phone,
      email,
      start,
      end,
    } = options;
    const params = {
      page,
      limit,
      sort,
      order,
      status,
      accountId,
      generalId,
      accountType,
      countryNumber,
      nickname,
      ...(phone !== "" && { phone }),
      ...(email !== "" && { email }),
      ...(start &&
        start !== "" && {
          start: `${start}T00:00:00`,
        }),
      ...(end && end !== "" && { end: `${end}T23:59:59` }),
    };
    console.log(params);
    const { data } = await AuthInstance.get("/backoffice-pet/accounts", {
      params,
    });

    return data;
  };

  return useQuery<GetAppUserResponseType>({
    queryKey: ["getAppUsers"],
    queryFn: fetcher,
  });
};
