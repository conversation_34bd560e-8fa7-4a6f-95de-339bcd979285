import { useRecoilValue } from "recoil";
import { AuthInstance } from "../index";
import { subjectIdAtom } from "@/atoms";
import { useQuery } from "@tanstack/react-query";
import { GetBookmarkResponseType } from "@/models";

export const useGetBookmarksQuery = (limit: number, page: number) => {
  const subjectId = useRecoilValue(subjectIdAtom);

  const fetcher = async () => {
    const { data } = await AuthInstance.get<GetBookmarkResponseType>(
      `/backoffice-pet/subjects/${subjectId}/solution/bookmarks`,
      {
        params: {
          limit,
          page,
        },
      }
    );

    return data;
  };

  return useQuery<GetBookmarkResponseType>({
    queryKey: ["getBookmarks"],
    queryFn: fetcher,
  });
};
