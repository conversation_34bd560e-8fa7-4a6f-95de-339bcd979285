import { useQuery } from "@tanstack/react-query";
import { AuthInstance } from "../index";
import { UserCareInformation } from "@/models";

export const useGetCaresCountQuery = (subjectId: number, start?: string) => {
  const fetcher = async () => {
    const { data } = await AuthInstance.get<UserCareInformation>(
      `/backoffice-pet/subjects/${subjectId}/cares`,
      {
        params: {
          start: start ? start : new Date().toISOString(),
        },
      }
    );
    return data;
  };

  return useQuery<UserCareInformation>({
    queryKey: ["getCaresCount"],
    queryFn: fetcher,
  });
};
