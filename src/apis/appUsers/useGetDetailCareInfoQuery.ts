import { useRecoilValue } from "recoil";
import { AuthInstance } from "../index";
import { subjectIdAtom } from "@/atoms";
import { useQuery } from "@tanstack/react-query";
import { GetDetailCareResponseType } from "@/models";

type CareType = "urine" | "water" | "weight";

interface GetDetialUserCareInfoQueryParams {
  start: string;
  end: string;
  utcOffset: string;
  periodType?: "year";
}

export const useGetDetailCareInfoQuery = (type: CareType, params: GetDetialUserCareInfoQueryParams) => {
  const subjectId = useRecoilValue<number | string>(subjectIdAtom);

  const fetcher = async () => {
    const { data } = await AuthInstance.get<GetDetailCareResponseType>(
      `/backoffice-pet/subjects/${subjectId}/cares/${type}`,
      {
        params,
      }
    );
    return data;
  };

  return useQuery<GetDetailCareResponseType>({
    queryKey: ["getDetailCareInfo"],
    queryFn: fetcher,
  });
};
