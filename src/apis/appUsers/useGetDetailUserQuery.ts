import { GetDetailUserProductionType } from "@/models";
import { AuthInstance } from "../index";
import { useQuery } from "@tanstack/react-query";
import { useSetRecoilState } from "recoil";
import { createdAtAtom, profileAtom, subjectIdAtom } from "@/atoms";

export const useGetDetailUserQuery = (accountId: number) => {
  const setProfileAtomState = useSetRecoilState<number>(profileAtom);
  const setSubjectIdState = useSetRecoilState<number | string>(subjectIdAtom);
  const setCreatedAtState = useSetRecoilState<string>(createdAtAtom);

  const fetcher = async () => {
    const { data } = await AuthInstance.get<GetDetailUserProductionType>(
      `/backoffice-pet/accounts/${accountId}`
    );

    setProfileAtomState(data?.subjects?.[0].id as number);
    setSubjectIdState(data?.subjects?.[0].id as number);

    if (data?.personalInfo?.createdAt)
      setCreatedAtState(data.personalInfo.createdAt);

    return data;
  };

  return useQuery<GetDetailUserProductionType>({
    queryKey: ["getDetailUser"],
    queryFn: fetcher,
  });
};
