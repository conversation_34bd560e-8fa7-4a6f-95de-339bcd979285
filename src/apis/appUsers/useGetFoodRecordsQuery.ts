import { subjectIdAtom } from "@/atoms";
import { useRecoilValue } from "recoil";
import { AuthInstance } from "../index";
import { FoodRecordsType } from "@/models";
import { useQuery } from "@tanstack/react-query";

export const useGetFoodRecordsQuery = (start: Date, end: Date, status: "Active" | "Deleted") => {
  const subjectId = useRecoilValue(subjectIdAtom);

  const fetcher = async () => {
    const { data } = await AuthInstance.get<FoodRecordsType>(
      `/backoffice-pet/subjects/${subjectId}/solution/records`,
      {
        params: {
          start: start.toISOString(),
          end: end.toISOString(),
          status: status.toLowerCase(),
        },
      }
    );

    return data;
  };

  return useQuery<FoodRecordsType>({
    queryKey: ["getFoodRecords"],
    queryFn: fetcher,
  });
};
