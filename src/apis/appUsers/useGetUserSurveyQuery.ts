import { useQuery } from "@tanstack/react-query";
import { AuthInstance } from "../index";
import { UserSurveyResponseType } from "@/models";

export const useGetUserSurveyQuery = (subjectId: number | string) => {
  const fetcher = async () => {
    const { data } = await AuthInstance.get<UserSurveyResponseType>(
      `/backoffice-pet/subjects/${subjectId}/survey`
    );
    return data;
  };

  return useQuery<UserSurveyResponseType>({
    queryFn: fetcher,
    queryKey: ["getUserSurvey"],
  });
};
