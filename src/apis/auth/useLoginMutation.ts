import type { SignInRequestType, SignInResponseType } from "@/models";
import { instance } from "..";
import { useMutation } from "@tanstack/react-query";
import base64 from "base-64";
import { useSetRecoilState } from "recoil";
import { auth } from "@/atoms";
import { useNavigate } from "react-router-dom";
import { isAxiosError } from "axios";
import { useSecurity } from "@/hooks";

export const useLoginMutation = (userData: SignInRequestType) => {
  const setToken = useSetRecoilState(auth);
  const navigate = useNavigate();
  const { handleSQLInjectionCheckSubmit } = useSecurity();

  const fetcher = async () => {
    const { data } = await instance.post<SignInResponseType>(
      "/backoffice-pet/admin/signin",
      userData
    );

    return data;
  };

  return useMutation<SignInResponseType>({
    mutationFn: () => {
      const { account } = userData;

      if (handleSQLInjectionCheckSubmit(account)) {
        // id에 특수문자 있나 확인(SQL Injection 방지)
        throw new Error(
          "Security: Special characters (%, =,*, >, <) cannot be entered."
        );
      }

      return fetcher();
    },
    onSuccess: (res) => {
      const { accessToken, id: userId } = res;
      const payload = accessToken.substring(
        accessToken.indexOf(".") + 1,
        accessToken.lastIndexOf(".")
      );
      const decoded = base64.decode(payload);
      const { exp } = JSON.parse(decoded);

      sessionStorage.exp = exp;
      sessionStorage.auth = accessToken;
      sessionStorage.userId = userId;

      setToken(accessToken);

      navigate("/");
    },
    onError: (err) => {
      console.error(err);

      if (isAxiosError(err)) {
        alert(err.response?.data.error.message);

        return;
      }

      if (err.message) {
        alert(err.message);
        return;
      }

      alert("Failed to login.");
    },
  });
};
