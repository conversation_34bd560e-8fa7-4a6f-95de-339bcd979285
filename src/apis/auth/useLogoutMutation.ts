import { useNavigate } from "react-router-dom";
import { AuthInstance } from "..";
import { useMutation } from "@tanstack/react-query";

export const useLogoutMutation = () => {
  const navigate = useNavigate();

  const fetcher = async () => {
    return await AuthInstance.delete("/backoffice-pet/admin/signout");
  };

  return useMutation({
    mutationFn: fetcher,
    onSuccess: (res) => {
      console.log(res);

      sessionStorage.clear();

      navigate("/login");
    },
    onError: (err) => {
      console.error(err);
      alert(`로그아웃에 실패하였습니다. 다시 시도해주세요.`);
    },
  });
};
