import { useQuery } from "@tanstack/react-query";
import { AuthInstance } from "..";
import { DashBoardResponseType } from "@/models";

export const useGetDashBoardDataQuery = () => {
  const token = sessionStorage.getItem("auth");

  const fetcher = async () => {
    const { data } = await AuthInstance.get<DashBoardResponseType>(
      "/backoffice-pet/dashboard",
      {
        params: {
          utcOffset: 9,
        },
      }
    );

    return data;
  };

  return useQuery<DashBoardResponseType>({
    queryKey: ["dashboard"],
    queryFn: fetcher,
    enabled: token !== null,
  });
};
