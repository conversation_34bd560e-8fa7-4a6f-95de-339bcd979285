import { useQuery } from "@tanstack/react-query";
import { AuthInstance } from "..";
import type { AccountCountryResponseType } from "@/models";

export const useGetAccountCountryQuery = () => {
  const fetcher = async () => {
    const { data } = await AuthInstance.get<AccountCountryResponseType>(
      "/backoffice-pet/statistics/analysis/account_country"
    );

    return data;
  };

  return useQuery<AccountCountryResponseType>({
    queryFn: fetcher,
    queryKey: ["accountCountry"],
  });
};
