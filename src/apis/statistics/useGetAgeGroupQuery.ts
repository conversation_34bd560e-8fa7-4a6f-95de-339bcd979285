import { useQuery } from "@tanstack/react-query";
import { AuthInstance } from "..";
import { AgeGroupResponseType } from "@/models";

export const useGetAgeGroupQuery = () => {
  const fetcher = async () => {
    const { data } = await AuthInstance.get<AgeGroupResponseType>(
      `/backoffice-pet/statistics/subjects/age_groups`
    );

    return data;
  };

  return useQuery<AgeGroupResponseType>({
    queryKey: ["getAgeGroup"],
    queryFn: fetcher,
  });
};
