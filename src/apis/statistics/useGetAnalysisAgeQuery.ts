import { useQuery } from "@tanstack/react-query";
import { AuthInstance } from "..";
import { AnalysisAgeResponseType } from "@/models";

export const useGetAnalysisAgeQuery = () => {
  const fetcher = async () => {
    const { data } = await AuthInstance.get<AnalysisAgeResponseType>(
      `/backoffice-pet/statistics/analysis/age_groups`
    );

    return data;
  };

  return useQuery<AnalysisAgeResponseType>({
    queryKey: ["getAnalysisAge"],
    queryFn: fetcher,
  });
};
