import { useQuery } from "@tanstack/react-query";
import { AuthInstance } from "..";
import { DiseaseResponseType } from "@/models";

export const useGetDiseasesQuery = () => {
  const fetcher = async () => {
    const { data } = await AuthInstance.get<DiseaseResponseType>(
      `/backoffice-pet/statistics/analysis/diseases`
    );

    return data;
  };

  return useQuery<DiseaseResponseType>({
    queryKey: ["getDiseases"],
    queryFn: fetcher,
  });
};
