import { useQuery } from "@tanstack/react-query";
import { AuthInstance } from "..";
import { MonthlyAnalysisResponseType } from "@/models";

export const useGetMonthlyAnalysisQuery = (nowDate: string) => {
  const fetcher = async () => {
    const startDate = new Date(nowDate);
    startDate.setFullYear(startDate.getFullYear() - 1);
    const { data } = await AuthInstance.get<MonthlyAnalysisResponseType>(
      `/backoffice-pet/statistics/analysis/months`,
      {
        params: {
          start: startDate.toISOString(),
          end: nowDate,
        },
      }
    );

    return data;
  };

  return useQuery<MonthlyAnalysisResponseType>({
    queryKey: ["getMonthlyAnalysis"],
    queryFn: fetcher,
  });
};
