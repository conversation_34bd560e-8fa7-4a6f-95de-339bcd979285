import {
  MonthRegisterSexRequestType,
  MonthlySexArrayResponseType,
} from "@/models";
import { AuthInstance } from "..";
import { useQuery } from "@tanstack/react-query";

export const useGetMonthlyRegisterQuery = (
  params: MonthRegisterSexRequestType
) => {
  const fetcher = async () => {
    const { data } = await AuthInstance.get<MonthlySexArrayResponseType>(
      `/backoffice-pet/statistics/accounts/registered_months`,
      { params }
    );

    return data;
  };

  return useQuery<MonthlySexArrayResponseType>({
    queryKey: ["getMonthlyRegister"],
    queryFn: fetcher,
  });
};
