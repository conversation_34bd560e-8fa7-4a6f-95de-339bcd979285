import { useQuery } from "@tanstack/react-query";
import { AuthInstance } from "..";
import { RegisterContriesResponseType } from "@/models/statistics";

export const useGetRegisteredContriesQuery = () => {
  const fetcher = async () => {
    const { data } = await AuthInstance.get<RegisterContriesResponseType>(
      `/backoffice-pet/statistics/accounts/registered_countries`
    );

    return data;
  };

  return useQuery<RegisterContriesResponseType>({
    queryKey: ["getRegisteredContries"],
    queryFn: fetcher,
  });
};
