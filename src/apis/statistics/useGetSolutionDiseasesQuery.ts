import {
  SolutionDiseasesRequestType,
  SolutionDiseasesResponseType,
} from "@/models";
import { AuthInstance } from "..";
import { useQuery } from "@tanstack/react-query";

export const useGetSolutionDiseasesQuery = (
  params: SolutionDiseasesRequestType
) => {
  const fetcher = async () => {
    const { data } = await AuthInstance.get<SolutionDiseasesResponseType>(
      `/backoffice-pet/statistics/survey/solution_diseases`,
      {
        params,
      }
    );

    return data;
  };

  return useQuery<SolutionDiseasesResponseType>({
    queryKey: ["getSolutionDiseases", params.choiceId, params.questionId],
    queryFn: fetcher,
  });
};
