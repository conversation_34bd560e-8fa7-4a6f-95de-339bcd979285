import { useQuery } from "@tanstack/react-query";
import { AuthInstance } from "..";
import { SurveyAnswerResponseType } from "@/models";

export const useGetSurveyAnswerQuery = (questionId: number) => {
  const fetcher = async () => {
    const { data } = await AuthInstance.get<SurveyAnswerResponseType>(
      `/backoffice-pet/statistics/survey/answers`,
      {
        params: {
          questionId,
        },
      }
    );

    return data;
  };

  return useQuery<SurveyAnswerResponseType>({
    queryKey: ["getSurveyAnswer", questionId],
    queryFn: fetcher,
  });
};
