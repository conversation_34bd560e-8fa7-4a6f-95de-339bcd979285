import { useQuery } from "@tanstack/react-query";
import { AuthInstance } from "..";

export const useGetSurveyQuery = (subjectId: number) => {
  const fetcher = async () => {
    const { data } = await AuthInstance.get(
      `/backoffice-pet/subjects/${subjectId}/survey`,
      {
        params: {
          subjectId,
        },
      }
    );

    return data;
  };

  return useQuery({
    queryKey: ["getSurvey", subjectId],
    queryFn: fetcher,
  });
};
