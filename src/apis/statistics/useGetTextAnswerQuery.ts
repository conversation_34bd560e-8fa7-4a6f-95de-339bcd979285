import { useQuery } from "@tanstack/react-query";
import { AuthInstance } from "..";
import { TextAnswerResponseType, TextAnswserRequestType } from "@/models";

export const useGetTextAnswerQuery = (params: TextAnswserRequestType) => {
  const fetcher = async () => {
    const { data } = await AuthInstance.get<TextAnswerResponseType>(
      `/backoffice-pet/survey/text_answers`,
      {
        params,
      }
    );

    return data;
  };

  return useQuery<TextAnswerResponseType>({
    queryKey: ["getTextAnswer"],
    queryFn: fetcher,
  });
};
