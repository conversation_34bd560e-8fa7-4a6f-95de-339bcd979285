import { useQuery } from "@tanstack/react-query";
import { AuthInstance } from "..";
import { UsingResponseType } from "@/models";

export const useGetUsingTimeQuery = (offset: number) => {
  const fetcher = async () => {
    const { data } = await AuthInstance.get<UsingResponseType>(
      `/backoffice-pet/statistics/analysis/hours`,
      {
        params: {
          utcOffset: offset,
        },
      }
    );

    return data.sort((a, b) => {
      const aHour = Number(a.hour.split(":")[0]);
      const bHour = Number(b.hour.split(":")[0]);

      return aHour - bHour;
    });
  };

  return useQuery<UsingResponseType>({
    queryFn: fetcher,
    queryKey: ["getUsingTime"],
  });
};
