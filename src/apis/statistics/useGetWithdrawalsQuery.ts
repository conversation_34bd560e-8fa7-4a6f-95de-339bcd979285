import { OpinionResponseType, OpinionsRequestType } from "@/models";
import { AuthInstance } from "..";
import { useQuery } from "@tanstack/react-query";

export const useGetWithdrawalsQuery = (params: OpinionsRequestType) => {
  const fetcher = async () => {
    const { data } = await AuthInstance.get<OpinionResponseType>(
      `/backoffice-pet/opinions`,
      { params }
    );

    return data;
  };

  return useQuery<OpinionResponseType>({
    queryKey: ["getWithdrawals", params.page],
    queryFn: fetcher,
  });
};
