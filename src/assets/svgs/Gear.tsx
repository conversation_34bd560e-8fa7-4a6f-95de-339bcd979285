import { HTMLAttributes } from "react";

interface GearSvgProps extends HTMLAttributes<SVGElement> {
  width?: string | number;
  height?: string | number;
  color: string;
}

export const Gear = ({ width, height, color, ...rest }: GearSvgProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      focusable="false"
      role="img"
      width={width}
      height={height}
      preserveAspectRatio="xMidYMid meet"
      viewBox="0 0 24 24"
      data-icon="grommet-icons:performance"
      color={color}
      {...rest}
    >
      <path
        fill="none"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
        d="M12 19l-2 3l-3-1l-.5-3.5L3 17l-1-3l3-2l-3-2l1-3l3.5-.5L7 3l3-1l2 3l2-3l3 1l.5 3.5L21 7l1 3l-3 2l3 2l-1 3l-3.5.5L17 21l-3 1l-2-3zm0-3a4 4 0 1 0 0-8a4 4 0 0 0 0 8z"
      ></path>
    </svg>
  );
};
