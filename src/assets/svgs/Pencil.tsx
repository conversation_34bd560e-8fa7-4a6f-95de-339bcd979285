import { HTMLAttributes } from "react";

interface PencilSVGProps extends HTMLAttributes<SVGElement> {
  width?: number | string;
  height?: number | string;
  color: string;
}

export const Pencil = ({ width, height, color, ...rest }: PencilSVGProps) => {
  return (
    <svg
      width={width}
      height={height}
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      focusable="false"
      role="img"
      color={color}
      preserveAspectRatio="xMidYMid meet"
      viewBox="0 0 24 24"
      data-icon="grommet-icons:form-edit"
      cursor="pointer"
      {...rest}
    >
      <path
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        d="M11.996 8.336l3.497 3.498l-3.497-3.498zm5.54-.54a.994.994 0 0 1-.004 1.416l-7.451 7.451L6 17.83l1.166-4.08l7.451-7.452a.997.997 0 0 1 1.416-.005l1.504 1.504z"
      ></path>
    </svg>
  );
};
