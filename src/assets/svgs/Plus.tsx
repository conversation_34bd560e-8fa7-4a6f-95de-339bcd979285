import { HTMLAttributes } from "react";

interface PlusSVGProps extends HTMLAttributes<SVGElement> {
  width?: number | string;
  height?: number | string;
  color: string;
}

export const Plus = ({ width, height, color, ...rest }: PlusSVGProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      focusable="false"
      role="img"
      width={width}
      height={height}
      preserveAspectRatio="xMidYMid meet"
      viewBox="0 0 24 24"
      data-icon="grommet-icons:form-add"
      color={color}
      {...rest}
    >
      <path
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        d="M12 18V6m-6 6h12"
      ></path>
    </svg>
  );
};
