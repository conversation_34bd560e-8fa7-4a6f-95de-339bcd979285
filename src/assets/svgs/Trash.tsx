import { HTMLAttributes } from "react";
import styled from "@emotion/styled";

interface TrashSVGProps extends HTMLAttributes<SVGElement> {
  width?: number | string;
  height?: number | string;
  color: string;
}

export const Trash = ({ width, height, color, ...rest }: TrashSVGProps) => {
  return (
    <_Svg
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      focusable="false"
      role="img"
      width={width}
      height={height}
      color={color}
      preserveAspectRatio="xMidYMid meet"
      viewBox="0 0 24 24"
      data-icon="grommet-icons:form-trash"
      {...rest}
    >
      <path
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        d="M7.5 9h9v10h-9V9zM5 9h14M9.364 6h5v3h-5V6zm1.181 5v6m3-6v6"
      ></path>
    </_Svg>
  );
};

const _Svg = styled.svg`
  :hover {
    color: #ffffff;
  }
`;
