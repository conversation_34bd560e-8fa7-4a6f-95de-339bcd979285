import { HTMLAttributes } from "react";

interface XSvgProps extends HTMLAttributes<SVGElement> {
  width?: string | number;
  height?: string | number;
  alignSelf?: string;
}

export const XSvg = ({ width, height, alignSelf, ...rest }: XSvgProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      focusable="false"
      role="img"
      width={width}
      height={height}
      preserveAspectRatio="xMidYMid meet"
      viewBox="0 0 24 24"
      data-icon="grommet-icons:close"
      cursor="pointer"
      style={{ alignSelf }}
      {...rest}
    >
      <path
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        d="M3 3l18 18M3 21L21 3"
      ></path>
    </svg>
  );
};
