import { slideIn } from "@/styles/keyframes";
import { Keyframes } from "@emotion/react";
import { SerializableParam, atomFamily } from "recoil";

export interface ModalAnimationAtomFamilyType {
  id: SerializableParam;
  animation: Keyframes;
}

export const modalAnimationAtomFamily = atomFamily<
  ModalAnimationAtomFamilyType,
  SerializableParam
>({
  key: "modalAnimationFamily",
  default: (id: SerializableParam) => ({
    id,
    animation: slideIn,
  }),
});
