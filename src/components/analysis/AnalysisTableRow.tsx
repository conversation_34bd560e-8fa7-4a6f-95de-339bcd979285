import { TableCell, TableRow } from "@mui/material";
import { useImage, useModal } from "@/hooks";
import { Image } from "..";
import { getDateFormat } from "@/utils";
import { AnalysisErrorType } from "@/models";

interface Props<T> {
  data: T[];
}

export const AnalysisTableRow = <T extends AnalysisErrorType>({
  data,
}: Props<T>) => {
  const [, handleImageChange] = useImage();
  const { openModal } = useModal("crop");

  return (
    <>
      {data?.map((item, idx) => (
        <TableRow
          key={idx}
          sx={{
            ":hover": {
              background: "#ededed",
            },
            "&:last-child td, &:last-child th": {
              border: 0,
            },
          }}
        >
          <TableCell>{item.id}</TableCell>
          <TableCell>{item.subjectId}</TableCell>
          <TableCell>
            {item.image && (
              <Image
                src={item.image}
                onClick={() => {
                  handleImageChange(item.image ?? "");
                  openModal();
                }}
                alt={item.createdAt}
                loading="lazy"
                width="20px"
                height="20px"
              />
            )}
          </TableCell>
          <TableCell>{item.type}</TableCell>
          <TableCell>{getDateFormat(item.createdAt)}</TableCell>
        </TableRow>
      ))}
    </>
  );
};
