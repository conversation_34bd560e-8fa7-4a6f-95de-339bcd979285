import {
  TableContainer,
  Table,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@mui/material";
import { CsvSvg } from "@/assets";
import { Image } from "..";
import { getDateFormat } from "@/utils";
import { useImage, useModal } from "@/hooks";
import { useGetAnalysisBoatQuery } from "@/apis/analysis";
import { useRecoilValue } from "recoil";
import { subjectIdAtom } from "@/atoms";

export const AnalysisInformation = () => {
  const [, handleImageChange] = useImage();
  const { openModal: openCropModal } = useModal("crop");
  const { openModal: openMaskModal } = useModal("mask");
  const subjectIdAtomValue = useRecoilValue(subjectIdAtom);
  const { data } = useGetAnalysisBoatQuery({
    limit: 15,
    page: 1,
    sort: "createdAt",
    order: "DESC",
    subjectId: Number(subjectIdAtomValue),
  });
  const imageRegExp = /(https?:\/\/.*\.(?:png|jpg|gif))/i;

  return (
    <>
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>ID</TableCell>
              <TableCell>Blood</TableCell>
              <TableCell>PH</TableCell>
              <TableCell>Protein</TableCell>
              <TableCell>Glucose</TableCell>
              <TableCell>Ketone</TableCell>
              <TableCell>Crop</TableCell>
              <TableCell>Mask</TableCell>
              <TableCell>Created</TableCell>
              <TableCell>CSV</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {data?.boats.map((item) => (
              <TableRow key={item.id}>
                <TableCell sx={{ border: 0 }}>{item.id}</TableCell>
                <TableCell sx={{ border: 0 }}>{item.blood}</TableCell>
                <TableCell sx={{ border: 0 }}>{item.ph}</TableCell>
                <TableCell sx={{ border: 0 }}>{item.protein}</TableCell>
                <TableCell sx={{ border: 0 }}>{item.glucose}</TableCell>
                <TableCell sx={{ border: 0 }}>{item.ketone}</TableCell>
                <TableCell sx={{ border: 0 }}>
                  {imageRegExp.test(item.crop) ? (
                    <Image
                      src={item.crop}
                      alt="crop image"
                      width="20px"
                      height="25px"
                      onClick={() => {
                        handleImageChange(item.crop ?? "");
                        openCropModal();
                      }}
                    />
                  ) : (
                    <span>Dummy</span>
                  )}
                </TableCell>
                <TableCell sx={{ border: 0 }}>
                  {imageRegExp.test(item.mask) ? (
                    <Image
                      src={item.mask}
                      alt="mask image"
                      width="20px"
                      height="25px"
                      onClick={() => {
                        handleImageChange(item.mask ?? "");
                        openMaskModal();
                      }}
                    />
                  ) : (
                    <span>Dummy</span>
                  )}
                </TableCell>
                <TableCell sx={{ border: 0 }}>
                  {getDateFormat(item.createdAt)}
                </TableCell>
                <TableCell sx={{ border: 0 }}>
                  <CsvSvg color="#04a954" width="1rem" height="1rem" />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );
};
