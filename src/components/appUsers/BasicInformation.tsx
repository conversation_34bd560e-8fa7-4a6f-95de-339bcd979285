import { profileAtom } from "@/atoms";
import type { GetDetailUserProductionType } from "@/models";
import { getDateFormat } from "@/utils";
import styled from "@emotion/styled";
import { useQueryClient } from "@tanstack/react-query";
import { useRecoilValue } from "recoil";
import { MouseEvent } from "react";
import { css } from "@emotion/react";
import { useNavigate } from "react-router-dom";
import { useModal } from "@/hooks";

export const BasicInformation = () => {
  const queryClient = useQueryClient();
  const { closeModal } = useModal("detailProfile");
  const data = queryClient.getQueryData<GetDetailUserProductionType>([
    "getDetailUser",
  ]);
  const profileState = useRecoilValue(profileAtom);
  const profileData = data?.subjects?.find(
    (subject) => subject.id === profileState
  );

  const navigate = useNavigate();

  const handleShowLibraryClick = (e: MouseEvent<HTMLSpanElement>) => {
    const subjectId: string | null = e.currentTarget.textContent;

    if (subjectId === null) {
      alert("subjectId가 비어있습니다. 다시 시도해주세요.");
      return;
    }

    navigate(`/analysis/failure?subjectId=${subjectId}`);
    closeModal();
  };

  return (
    <_Wrapper>
      {data?.subjects && (
        <>
          <_Nav>
            <_TitleContainer>
              <_TitleLine />
              <_Title>Profile</_Title>
            </_TitleContainer>
            <_Row>
              <_Cell>
                <_InnerTitle>Subject Id</_InnerTitle>
                <_Content onClick={handleShowLibraryClick} showUnderLine={true}>
                  {profileData?.id}
                </_Content>
              </_Cell>
              <_Cell>
                <_InnerTitle>Gender</_InnerTitle>
                <_Content>
                  {profileData?.sex === "m" ? "Male" : "Female"}
                </_Content>
              </_Cell>
              <_Cell>
                <_InnerTitle>Date of Brith</_InnerTitle>
                <_Content>{profileData?.birth}</_Content>
              </_Cell>
              <_Cell>
                <_InnerTitle>Weight</_InnerTitle>
                <_Content>{profileData?.initialWeight}</_Content>
              </_Cell>
              <_Cell>
                <_InnerTitle>Target Weight</_InnerTitle>
                <_Content>{profileData?.targetWeight}</_Content>
              </_Cell>
              <_Cell>
                <_InnerTitle>Height</_InnerTitle>
                <_Content>{profileData?.metadata.height}</_Content>
              </_Cell>
              <_Cell>
                <_InnerTitle>Target Calories</_InnerTitle>
                <_Content>{profileData?.metadata.targetCalorie}</_Content>
              </_Cell>
              <_Cell>
                <_InnerTitle>Keytone Mode</_InnerTitle>
                <_Content>Yes</_Content>
              </_Cell>
            </_Row>
          </_Nav>
          <_Nav>
            <_TitleContainer>
              <_TitleLine />
              <_Title>Account</_Title>
            </_TitleContainer>
            <_Cell>
              <_InnerTitle>Account ID</_InnerTitle>
              <_Content>{profileData?.accountId}</_Content>
            </_Cell>
            <_InnerCell>
              <_Th>Registration</_Th>
              <_Wrapper>
                <_RowInnerTitle>Type</_RowInnerTitle>
                <_Content>
                  {data.kakao
                    ? "Kakao"
                    : data.google
                    ? "Google"
                    : data.apple
                    ? "Apple "
                    : data.general}
                </_Content>
              </_Wrapper>
              <_Wrapper>
                <_RowInnerTitle>Date</_RowInnerTitle>
                <_Content>
                  {getDateFormat(profileData?.createdAt as string)}
                </_Content>
              </_Wrapper>
            </_InnerCell>
            <_Cell>
              <_InnerTitle>Status</_InnerTitle>
              <_Content>{data.status}</_Content>
            </_Cell>
            <_Cell>
              <_InnerTitle>Contry Code</_InnerTitle>
              <_Content>{data.personalInfo.countryNumber}</_Content>
            </_Cell>
            <_Cell>
              <_InnerTitle>Phone</_InnerTitle>
              <_Content>{data.personalInfo.phone}</_Content>
            </_Cell>
            <_Cell>
              <_InnerTitle>Email</_InnerTitle>
              <_Content>{data.personalInfo.email}</_Content>
            </_Cell>
          </_Nav>
        </>
      )}
    </_Wrapper>
  );
};

const _Wrapper = styled.div`
  width: 100%;
  display: flex;
`;

const _Nav = styled.nav`
  width: 50%;
`;

const _TitleLine = styled.div`
  width: 3px;
  height: 1rem;
  background: #41d8e6;
  margin-right: 10px;
`;

const _TitleContainer = styled.div`
  width: 100%;
  margin-top: 10px;
  display: flex;
  align-items: center;
`;

const _Title = styled.h4`
  color: #767676;
  font-size: 1.5rem;
  font-weight: 500;
  line-height: 1.2;
`;

const _Row = styled.div`
  display: flex;
  flex-direction: column;
`;

const _Cell = styled.div`
  display: flex;
  margin-top: 5px;
`;

const _InnerTitle = styled.span`
  width: 50%;
  font-size: 17px;
  font-family: "Noto Sans KR", sans-serif;
  margin-bottom: 0.5rem;
  color: #767676;
`;

const _Content = styled.span<{ showUnderLine?: boolean }>`
  font-size: 17px;
  font-family: "Noto Sans KR", sans-serif;
  margin-bottom: 0.5rem;
  ${({ showUnderLine }) =>
    showUnderLine &&
    css`
      text-decoration: underline;
      cursor: pointer;
    `}
`;

const _Th = styled(_InnerTitle)`
  width: 100%;
`;

const _InnerCell = styled(_Cell)`
  flex-direction: column;
`;

const _RowInnerTitle = styled(_InnerTitle)`
  text-align: end;
  padding-right: 10px;
`;
