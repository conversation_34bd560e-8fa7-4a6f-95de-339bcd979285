import styled from "@emotion/styled";
import { Image } from "@/components";

interface BookMarkItemProps {
  english: string;
  korean: string;
  kcal: number;
  weight: number;
}

export const BookMarkItem = ({
  english,
  korean,
  kcal,
  weight,
}: BookMarkItemProps) => {
  return (
    <_Wrapper>
      <Image
        src={`https://food-cym-s3.s3.ap-northeast-2.amazonaws.com/cym/${english}.jpg`}
        alt="food image"
        width="120px"
        height="120px"
      />
      <span>{korean}</span>
      <_FoodInformationWrapper>
        <span>{kcal}kcal</span>
        <span>{weight}g</span>
      </_FoodInformationWrapper>
    </_Wrapper>
  );
};

const _Wrapper = styled.div`
  max-width: 120px;
`;

const _FoodInformationWrapper = styled.div`
  width: 100%;
  display: flex;
  justify-content: space-between;
`;
