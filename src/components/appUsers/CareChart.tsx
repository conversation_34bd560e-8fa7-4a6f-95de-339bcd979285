import {
  Chart,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Line } from "react-chartjs-2";

Chart.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface CareChartProps {
  records?: {
    date: string;
    values: {
      id: number;
      value: number;
      createdAt: string;
    }[];
  }[];
  montlyData?: {
    date: string;
    value: number;
  }[];
  target: number;
  condition: string;
}

export const CareChart = ({
  records,
  target,
  montlyData,
  condition,
}: CareChartProps) => {
  return (
    <>
      <Line
        data={{
          labels:
            condition === "년"
              ? montlyData?.map((value) => value.date)
              : records?.map((value) => value.date),
          datasets: [
            {
              label: "target weight",
              data:
                condition === "년"
                  ? montlyData?.map(() => target)
                  : records?.map(() => target),
              borderColor: "#41d8e6",
              borderDash: [5, 5],
            },
            {
              label: "weight",
              data:
                condition === "년"
                  ? montlyData?.map((value) => value.value)
                  : records?.map((value) => value.values[0].value),
              borderColor: "#3e95cd",
            },
          ],
        }}
        options={{
          plugins: {
            legend: {
              display: false,
            },
            datalabels: {
              display: false,
            },
          },
        }}
      />
    </>
  );
};
