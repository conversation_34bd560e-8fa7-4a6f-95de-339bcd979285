import { CymWeight } from "@/assets";
import { Image } from "..";
import styled from "@emotion/styled";
import { useCareDetail, useModal } from "@/hooks";
import { useRecoilValue } from "recoil";
import { createdAtAtom, subjectIdAtom } from "@/atoms";
import { useGetCaresCountQuery } from "@/apis/appUsers/useGetCaresCountQuery";

export const CareInformation = () => {
  const { openModal: openDetailModal } = useModal("detailInformation");
  const [, handleChange] = useCareDetail();
  const subjectState = useRecoilValue<number | string>(subjectIdAtom);
  const createdAtState = useRecoilValue(createdAtAtom);
  const { data } = useGetCaresCountQuery(
    subjectState as number,
    createdAtState
  );

  const onClick = (idx: number) => {
    const menu: string[] = ["체중"];

    if (idx > menu.length) throw new Error("index invalid");

    handleChange(menu[idx]);
    openDetailModal();
  };

  return (
    <_Wrapper>
      <_Nav>
        <_TitleContainer>
          <_TitleLine />
          <_Title onClick={() => onClick(0)}>
            체중 Care <_H4 />
          </_Title>
        </_TitleContainer>
        <_Wrapper>
          <_InnerNav>
            <Image src={CymWeight} alt="weight" />
            <p>kg</p>
          </_InnerNav>
          <_InnerNav>
            <span>{data?.weight}</span>
          </_InnerNav>
        </_Wrapper>
      </_Nav>
    </_Wrapper>
  );
};

const _Wrapper = styled.div`
  width: 100%;
  display: flex;
`;
const _Nav = styled.nav`
  width: 33%;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const _TitleLine = styled.div`
  width: 3px;
  height: 1rem;
  background: #41d8e6;
  margin-right: 10px;
`;

const _TitleContainer = styled.div`
  width: 100%;
  margin-top: 10px;
  display: flex;
  align-items: center;
`;

const _Title = styled.span`
  color: #767676;
  font-size: 1.5rem;
  font-weight: 500;
  line-height: 1.2;
  display: flex;
  align-items: center;
  cursor: pointer;
`;

const _H4 = styled.h4`
  ::after {
    content: "\\f054";
    cursor: initial;
    font-size: 1rem;
    font-family: "Font Awesome 5 Free";
    margin-left: 0.5rem;
    position: relative;
  }
`;

const _InnerNav = styled(_Nav)`
  width: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 10px;

  > span {
    font-size: 2rem;
    font-weight: bold;
    color: #000;
  }
`;
