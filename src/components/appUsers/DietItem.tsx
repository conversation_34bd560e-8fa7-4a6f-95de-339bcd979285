import styled from "@emotion/styled";

interface DietItemProps {
  kcal: number;
  type: "아침" | "점심" | "저녁" | "간식";
}

export const DietItem = ({ kcal, type }: DietItemProps) => {
  return (
    <_Wrapper>
      <_ValueText>{kcal}</_ValueText>
      <_KcalText>kcal</_KcalText>
      <_ValueText>{type}</_ValueText>
    </_Wrapper>
  );
};

const _Wrapper = styled.div`
  width: 120px;
  height: 140px;
  padding: 10px;
  background: #8080804d;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  color: #212529;
  border-radius: 10px;
  margin-right: 10px;
`;

const _ValueText = styled.span`
  font-size: 1.7rem;
`;

const _KcalText = styled.span`
  font-size: 17px;
`;
