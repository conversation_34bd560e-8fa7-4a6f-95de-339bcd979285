import { useGetUserSurveyQuery } from "@/apis/appUsers";
import styled from "@emotion/styled";
import { useRecoilValue } from "recoil";
import { subjectIdAtom } from "@/atoms";

export const HealthInformation = () => {
  const subjectIdState = useRecoilValue<number | string>(subjectIdAtom);
  const { data } = useGetUserSurveyQuery(subjectIdState);
  return (
    <_Wrapper>
      <_Nav>
        <_TitleContainer>
          <_TitleLine />
          <_Title>생활습관</_Title>
        </_TitleContainer>
        {data?.lifeStyles.map((element) => (
          <_Cell key={element.question}>
            <_InnerTitle>{element.question}</_InnerTitle>
            <_Content>{element.text ?? "-"}</_Content>
          </_Cell>
        ))}
      </_Nav>
      <_Nav>
        <_TitleContainer>
          <_TitleLine />
          <_Title>식생활</_Title>
        </_TitleContainer>
        {data?.foodLifes.map((element) => (
          <_Cell key={element.question}>
            <_InnerTitle>{element.question}</_InnerTitle>
            <_Content>{element.text ?? "-"}</_Content>
          </_Cell>
        ))}
      </_Nav>
    </_Wrapper>
  );
};

const _Wrapper = styled.div`
  width: 100%;
  display: flex;
`;

const _Nav = styled.nav`
  width: 50%;
`;

const _TitleLine = styled.div`
  width: 3px;
  height: 1rem;
  background: #41d8e6;
  margin-right: 10px;
`;

const _TitleContainer = styled.div`
  width: 100%;
  margin-top: 10px;
  display: flex;
  align-items: center;
`;

const _Title = styled.h4`
  color: #767676;
  font-size: 1.5rem;
  font-weight: 500;
  line-height: 1.2;
`;

const _Cell = styled.div`
  display: flex;
  margin-top: 5px;
`;

const _InnerTitle = styled.span`
  width: 50%;
  font-size: 17px;
  font-family: "Noto Sans KR", sans-serif;
  margin-bottom: 0.5rem;
  color: #767676;
`;

const _Content = styled.span`
  font-size: 17px;
  font-family: "Noto Sans KR", sans-serif;
  margin-bottom: 0.5rem;
`;
