import styled from "@emotion/styled";
import { BookMarkItem, DietItem } from ".";
import { useModal } from "@/hooks";
import { useGetBookmarksQuery, useGetFoodRecordsQuery } from "@/apis/appUsers";
import { useRecoilValue } from "recoil";
import { statusAtom } from "@/atoms";

export const SolutionInformation = () => {
  const { openModal } = useModal("dietRecord");
  const statusState = useRecoilValue(statusAtom);
  const getNewDate = () => new Date();
  const { data: bookmarkItems } = useGetBookmarksQuery(10, 1);
  const { data: foodRecords } = useGetFoodRecordsQuery(
    new Date(getNewDate().setDate(getNewDate().getDate() - 1)),
    getNewDate(),
    statusState.status as "Active" | "Deleted"
  );
  return (
    <>
      <_TitleContainer>
        <_TitleLine />
        <_Title>즐겨찾기</_Title>
      </_TitleContainer>
      <_ItemWrapper>
        {bookmarkItems?.bookmarks.map((element) => (
          <BookMarkItem
            key={element.id}
            english={element.food.english}
            korean={element.food.korean}
            kcal={element.food.calorie}
            weight={element.food.amount}
          />
        ))}
      </_ItemWrapper>
      <_TitleContainer>
        <_TitleLine />
        <_Title onClick={openModal}>
          내 식단 <_H4 />
        </_Title>
      </_TitleContainer>
      <_ItemWrapper>
        <DietItem kcal={foodRecords?.breakfastCalories ?? 0} type="아침" />
        <DietItem kcal={foodRecords?.lunchCalories ?? 0} type="점심" />
        <DietItem kcal={foodRecords?.dinnerCalories ?? 0} type="저녁" />
        <DietItem kcal={foodRecords?.snackCalories ?? 0} type="간식" />
      </_ItemWrapper>
    </>
  );
};

const _TitleLine = styled.div`
  width: 3px;
  height: 1rem;
  background: #41d8e6;
  margin-right: 10px;
`;

const _TitleContainer = styled.div`
  width: 100%;
  margin-top: 10px;
  display: flex;
  align-items: center;
`;

const _Title = styled.h4`
  color: #767676;
  font-size: 1.5rem;
  font-weight: 500;
  line-height: 1.2;
  display: flex;
  align-items: center;
`;

const _H4 = styled.h4`
  ::after {
    content: "\\f054";
    cursor: initial;
    font-size: 1rem;
    font-family: "Font Awesome 5 Free";
    margin-left: 0.5rem;
    position: relative;
  }
`;

const _ItemWrapper = styled.div`
  display: flex;
  flex-wrap: wrap;
`;
