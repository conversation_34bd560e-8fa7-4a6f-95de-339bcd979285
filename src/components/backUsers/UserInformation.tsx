import { Pencil } from "@/assets";
import { getDateFormat } from "@/utils";
import styled from "@emotion/styled";
import { useModal, useSelectUser } from "@/hooks";
import { useDetailAccountQuery } from "@/apis/admin";

interface Props {
  accountId: number;
}

export const UserInformation = ({ accountId }: Props) => {
  const { openModal } = useModal("userEdit");
  const { data } = useDetailAccountQuery(accountId);
  const [, handleChange] = useSelectUser();
  return (
    <>
      <_TitleContainer>
        <_TitleLine />
        User
        <_EditText
          onClick={() => {
            handleChange({
              id: data?.id as number,
              account: data?.account as string,
            });
            openModal();
          }}
        >
          <Pencil color="#41d8e6" width="1rem" height="1rem" />
          Edit
        </_EditText>
      </_TitleContainer>
      <_UserInformationRow>
        <_UserInformationCell>
          <_UserInformationTitle>ID</_UserInformationTitle>
          <_UserInformationContent>{data?.id}</_UserInformationContent>
        </_UserInformationCell>
        <_UserInformationCell>
          <_UserInformationTitle>Account</_UserInformationTitle>
          <_UserInformationContent>{data?.account}</_UserInformationContent>
        </_UserInformationCell>
        <_UserInformationCell>
          <_UserInformationTitle>Name</_UserInformationTitle>
          <_UserInformationContent>{data?.name}</_UserInformationContent>
        </_UserInformationCell>
        <_UserInformationCell>
          <_UserInformationTitle>Created</_UserInformationTitle>
          <_UserInformationContent>
            {getDateFormat(data?.createdAt as string)}
          </_UserInformationContent>
        </_UserInformationCell>
      </_UserInformationRow>
    </>
  );
};

const _TitleContainer = styled.h4`
  width: 100%;
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  color: #767676;
  font-weight: 500;
  line-height: 1.2;
  margin-bottom: 1rem;
`;

const _TitleLine = styled.div`
  width: 3px;
  height: 1rem;
  background: #41d8e6;
  margin-right: 20px;
`;

const _UserInformationRow = styled.div`
  display: flex;
  flex-direction: column;
`;

const _UserInformationCell = styled.div`
  display: flex;
`;

const _UserInformationTitle = styled.div`
  width: 20%;
  font-size: 17px;
  font-family: "Noto Sans KR", sans-serif;
  margin-bottom: 0.5rem;
`;

const _UserInformationContent = styled.div`
  font-size: 17px;
  font-family: "Noto Sans KR", sans-serif;
  margin-bottom: 0.5rem;
`;

const _EditText = styled.span`
  color: #41d8e6;
  font-size: 1rem;
  margin-left: 0.3rem;
  text-decoration: underline;
  cursor: pointer;
`;
