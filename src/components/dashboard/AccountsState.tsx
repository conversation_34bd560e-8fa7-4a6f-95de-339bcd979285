import ProgressBar from "./ProgressBar";
import { AccountCountsType } from "@/models";

interface Props {
  accountsObj: AccountCountsType;
}

interface StatusType {
  label: string;
  status: string;
  color: string;
}

const status: StatusType[] = [
  {
    label: "전체 계정 수(Active)",
    status: "activeStatusAccounts",
    color: "#41d8e6",
  },
  {
    label: "설문조사 시작 전(Survey Ready)",
    status: "surveyReadyStatusAccounts",
    color: "#F8BCCE",
  },
  {
    label: "설문조사 진행 중(Survey Ongoing)",
    status: "surveyOngoingStatusAccounts",
    color: "#E994D1",
  },
  {
    label: "직원용 계정(Archived)",
    status: "archivedStatusAccounts",
    color: "#B485EF",
  },
  {
    label: "삭제된 계정(Deleted)",
    status: "deletedStatusAccounts",
    color: "#8E92F4",
  },
  {
    label: "탈퇴 후 30일 보관 계정(Inactive)",
    status: "inactiveStatusAccounts",
    color: "#A5BACF",
  },
];

// tolowercase
// const regex = / /gi;
// text.toLowerCase().replace(regex, '')

const AccountsState = ({ accountsObj }: Props) => {
  // console.log(accountsObj);

  return (
    <>
      {accountsObj &&
        status.map((s, idx) => (
          <ProgressBar
            key={idx}
            color={s.color}
            label={s.label}
            amount={accountsObj[s.status as keyof typeof accountsObj]}
            total={accountsObj.total}
          />
        ))}
    </>
  );
};

export default AccountsState;
