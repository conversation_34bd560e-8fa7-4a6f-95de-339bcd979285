import { TestCountsType } from "@/models";
import { Chart as ChartJS, ArcElement, <PERSON><PERSON><PERSON>, Legend } from "chart.js";
import { Doughnut } from "react-chartjs-2";

ChartJS.register(ArcElement, Tooltip, Legend);

interface Props {
  chartData: TestCountsType;
}

const options = {
  plugins: {
    legend: {
      display: true,
    },
    tooltip: {
      enabled: true,
    },
    datalabels: {
      color: "#fff",
      font: { size: 16, family: "GilroyMedium" },
    },
  },
  rotation: -90,
  circumference: 180,
  cutout: "60%",
  maintainAspectRatio: true,
  responsive: true,
};

const DonutChart = ({ chartData }: Props) => {
  const resultData = [
    chartData?.success?.boat?.total,
    chartData?.error && chartData?.error?.boat?.total,
  ];
  const data = {
    labels: ["Success", "Failure"],
    datasets: [
      {
        data: resultData,
        backgroundColor: ["#BE9DE2", "#F490AE"],
        borderColor: "rgba(255, 255, 255, 0.60)",
      },
    ],
  };

  return (
    <div style={{ width: "300px" }}>
      <Doughnut
        width="100"
        height="300"
        data={data}
        style={{ position: "relative" }}
        options={options}
      />
    </div>
  );
};

export default DonutChart;
