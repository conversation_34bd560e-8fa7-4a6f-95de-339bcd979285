import styled from "@emotion/styled";
import { <PERSON><PERSON><PERSON><PERSON>, KakaoLogo, GoogleLogo, AppleLogo } from "@/assets/svgs/Icons";
import { AccountTypeCountsType } from "@/models";

interface Props {
  funnelObj: AccountTypeCountsType;
}

const funnel = [
  { icon: <CymLogo />, item: "general" },
  { icon: <KakaoLogo />, item: "kakao" },
  { icon: <GoogleLogo />, item: "google" },
  { icon: <AppleLogo />, item: "apple" },
];

const Funnel = ({ funnelObj }: Props) => {
  return (
    <_Container>
      {funnelObj &&
        funnel.map((funnel, idx) => (
          <_FunnelContents key={idx}>
            {funnel.icon}
            <_Label>{funnel.item}</_Label>
            <_Amount>
              {Object.prototype.hasOwnProperty.call(funnelObj, funnel.item) &&
                funnelObj[funnel.item as keyof typeof funnelObj]}
            </_Amount>
          </_FunnelContents>
        ))}
    </_Container>
  );
};

const _Container = styled.div`
  display: flex;
  margin-top: 10px;
  flex-direction: column;
  gap: 20px;
`;

const _FunnelContents = styled.div`
  // background-color: grey;
  height: 40px;
  display: flex;
  width: 100%;
  gap: 20px;
  justify-content: space-between;
  align-items: center;
  color: var(--color-text-dark);

  svg {
    border-radius: 100%;
    width: 40px;
    height: 40px;
  }
`;

const _Label = styled.div`
  font-family: "Gilroy";
  flex: auto;
  text-transform: capitalize;
`;

const _Amount = styled.div`
  font-family: "GilroyMedium";
  font-size: 1.2rem;
`;

export default Funnel;
