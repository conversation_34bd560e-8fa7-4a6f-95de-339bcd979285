import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ChartData,
  ChartOptions,
  TooltipModel,
} from "chart.js";
import { Line } from "react-chartjs-2";
import ChartDataLabels from "chartjs-plugin-datalabels";
import { WeeklyAccountCountsType, WeeklyTestCountsType } from "@/models";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface GraphData {
  graphData: WeeklyAccountCountsType[] | WeeklyTestCountsType[];
}

const LineChart = ({ graphData }: GraphData) => {
  const labels: Array<string> = [];
  const count: Array<number> = [];
  graphData?.map((data) =>
    labels.push(data.date.split("-").join(".").slice(5))
  );
  graphData?.map((data) => count.push(data.count));
  const max = Math.max(...count);
  // console.log(max);
  // const customTooltipHandler = (tooltipItem) => {
  //   console.log(tooltipItem)
  //   const total = tooltipItem.count;
  //   const kakao = tooltipItem.kakao;
  //   const google = tooltipItem.google;
  //   const apple = tooltipItem.apple;

  //   return `Total: ${total} | kakao: ${kakao} | Yellow: ${google} | apple: ${apple}`;
  // };

  // const customTooltips = {
  //   callbacks: {
  //     label: function (tooltipItem) {
  //       const dataIndex = tooltipItem.dataIndex;
  //       const total = graphData[dataIndex].count;
  //       const kakao = graphData[dataIndex].kakao;
  //       const google = graphData[dataIndex].google;
  //       const apple = graphData[dataIndex].apple;

  //       return `Total: ${total} \n kakao: ${kakao} | google: ${google} | apple: ${apple}`;
  //     },
  //     title: function (tooltipItem) {
  //       // Customize the title, if needed
  //       return `${tooltipItem[0].label}\n----------------`;
  //     },
  //   },
  //   // backgroundColor: "rgba(255, 255, 255, 0.8)",
  //   // borderColor: "rgba(0, 0, 0, 0.8)",
  //   // borderWidth: 1,
  //   // titleFontColor: "black",
  //   // bodyFontColor: "black",
  //   displayColors: false,
  // };

  const externalTooltipHandler = ({
    chart,
    tooltip,
  }: {
    chart: ChartJS<"line">;
    tooltip: TooltipModel<"line">;
  }) => {
    const tooltipEl = getOrCreateTooltip(chart);
    // console.log(tooltip);

    // Hide if no tooltip
    if (tooltip.opacity === 0) {
      tooltipEl.style.opacity = "0";
      return;
    }

    // Set Text
    let isAccountData = true;
    if (tooltip.body) {
      const titleLines = tooltip.title || [];
      const bodyLines = tooltip.body.map((b) => b.lines);

      const tableHead = document.createElement("thead");

      titleLines.forEach((title: string) => {
        const tr = document.createElement("tr");
        tr.style.borderWidth = "0";

        const th = document.createElement("th");
        th.style.borderWidth = "0";
        const text = document.createTextNode(title);

        th.appendChild(text);
        tr.appendChild(th);
        tableHead.appendChild(tr);
      });

      const tableBody = document.createElement("tbody");

      bodyLines.forEach((_, i) => {
        const colors = tooltip.labelColors[i];
        const index = tooltip.dataPoints[i].dataIndex;
        // const tooltipItem = tooltip?.dataPoints[i]?.dataset?.label[
        //   index
        // ] as unknown as WeeklyAccountCountsType | WeeklyTestCountsType;
        const tooltipItem = graphData[index];
        isAccountData =
          tooltipItem["kakao" as keyof typeof tooltipItem] !== undefined;
        // console.log(tooltipItem.kakao);
        // const label = chart.data.labels[tooltip.dataPoints[i].dataIndex];

        const span = document.createElement("span");
        span.style.background = colors.backgroundColor.toString();
        span.style.borderColor = colors.borderColor.toString();
        span.style.borderWidth = "2px";
        // span.style.marginRight = "10px";
        // span.style.height = "10px";
        // span.style.width = "10px";
        span.style.display = "inline-block";

        const tr = document.createElement("tr");
        tr.style.backgroundColor = "inherit";
        tr.style.borderWidth = "0";

        const td = document.createElement("td");
        td.style.borderWidth = "0";
        td.style.padding = "10px 0";

        const pickedData = customTooltipHandler(
          tooltipItem as unknown as
            | WeeklyAccountCountsType
            | WeeklyTestCountsType
        );

        const text = document.createTextNode(`${pickedData}`); // Include label in the tooltip text
        // const text = document.createTextNode(body);

        td.appendChild(span);
        td.appendChild(text);
        tr.appendChild(td);
        tableBody.appendChild(tr);
      });

      const tableRoot = tooltipEl.querySelector("table");

      // Remove old children
      while (tableRoot?.firstChild) {
        tableRoot?.firstChild.remove();
      }

      // Add new children
      tableRoot?.appendChild(tableHead);
      tableRoot?.appendChild(tableBody);
    }

    const { offsetLeft: positionX, offsetTop: positionY } = chart.canvas;

    // Display, position, and set styles for font
    tooltipEl.style.opacity = "0.6";
    tooltipEl.style.left = positionX + tooltip.caretX + 65 + "px";
    tooltipEl.style.top = isAccountData
      ? positionY + tooltip.caretY + -150 + "px"
      : positionY + tooltip.caretY + -130 + "px";
    // console.log(positionY, tooltip.caretY);
    // tooltipEl.style.font = tooltip.options.bodyFont.string; // 주석 이유: string 값이 들어가야하는데 toString으로 값이 들어가지 않음(로그에서 에러 발생)
    tooltipEl.style.textAlign = "center";
    tooltipEl.style.padding =
      tooltip.options.padding + "px " + tooltip.options.padding + "px";
    tooltipEl.style.width = "120px";
    tooltipEl.style.height = isAccountData ? "145px" : "125px";
    // tooltipEl.style.minHeight = "120px";
    // tooltipEl.style.maxHeight = "150px";
  };

  const options: ChartOptions<"line"> = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          color: "#c8c8c8",
          font: { size: 14, family: "GilroyMedium" },
        },
      },
      y: {
        display: true,
        max: max > 5 ? max + 2 : 5, // 최댓값 + 1,
        grid: {
          display: false,
        },
        ticks: {
          color: "#c8c8c8",
          font: { size: 14, family: "GilroyMedium" },
          stepSize: 1,
        },
      },
    },
    // labels: {},
    interaction: {
      intersect: false,
      mode: "index",
    },
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: false,
        position: "nearest",
        external: externalTooltipHandler,
      },
      datalabels: {
        display: false,
        // color: "#fff",
        font: { size: 16, family: "GilroyMedium" },
        // title: {
        //   display: true,
        //   text: (ctx) =>
        //     "Tooltip position mode: " +
        //     ctx.chart.options.plugins.tooltip.position,
        // },
        // formatter(value: number, context: any) {
        //   return value;
        // },
        // backgroundColor(context) {
        //   return context.dataset.backgroundColor;
        // },
        // borderColor: "white",
        // borderRadius: 10,
        // borderWidth: 2,
      },
    },
  };

  const data: ChartData<"line"> = {
    labels,
    datasets: [
      {
        data: count,
        // label: graphData,
        borderColor: "rgba(65, 216, 230, 0.7)",
        borderWidth: 4,
        pointBorderWidth: 0,
        hoverBorderWidth: 5,
        tension: 0.2,
        hoverBackgroundColor: "#ffffff",
        backgroundColor: "transparent",
      },
    ],
  };

  const getOrCreateTooltip = (chart: ChartJS<"line">) => {
    let tooltipEl = chart.canvas.parentNode?.querySelector("div");

    if (!tooltipEl) {
      tooltipEl = document.createElement("div");
      tooltipEl.style.background = "var(--color-underline-dark)";
      // tooltipEl.style.boxShadow = "1px 2px 6px rgba(0, 0, 0, 0.1)";
      tooltipEl.style.fontFamily = "GilroyMedium";
      // tooltipEl.style.border= "1px solid var(--color-bg-dark)",
      //   // backgroundColor: "rgba(255, 255, 255, 0.8)",
      tooltipEl.style.borderRadius = "10px";
      tooltipEl.style.color = "var(--color-text-dark)";
      tooltipEl.style.opacity = "1";
      tooltipEl.style.pointerEvents = "none";
      tooltipEl.style.padding = "10px 0";
      tooltipEl.style.position = "absolute";
      tooltipEl.style.transform = "translate(-50%, 0)";
      tooltipEl.style.transition = "all .1s ease";
      // tooltipEl.style.width = "20%";
      // tooltipEl.style.height = "100%";
      // tooltipEl.style.minHeight = "120px";
      // tooltipEl.style.maxHeight = "150px";
      tooltipEl.style.top = "0";
      // tooltipEl.style.right = "-10%";
      // tooltipEl.style.left = "50%";
      // tooltip.caretY === chart.chartArea.bottom ? "" : tooltip.positionY + tooltip.caretY - tooltip.height + "px";
      // tooltipEl.style.bottom = tooltip.caretY === chart.chartArea.bottom ? chart.chartArea.height / 2 + "px" : "";

      const table = document.createElement("table");
      table.style.padding = "10px";

      tooltipEl.appendChild(table);
      chart.canvas.parentNode?.appendChild(tooltipEl);
    }

    return tooltipEl;
  };

  const customTooltipHandler = (
    tooltipItem: WeeklyAccountCountsType | WeeklyTestCountsType
  ) => {
    const { count, kakao, google, apple } =
      tooltipItem as WeeklyAccountCountsType;
    const { success, error } = tooltipItem as WeeklyTestCountsType;
    // console.log(kakao === undefined);

    const accountItem = `Total: ${count}\nKakao: ${kakao}\nGoogle: ${google}\nApple: ${apple}`;
    const analysisItem = `Total: ${count}\nSuccess: ${success}\nError: ${error}`;
    return kakao === undefined ? analysisItem : accountItem;
  };

  return (
    <div style={{ height: "300px", position: "relative" }}>
      <Line
        width="300"
        options={options}
        data={data}
        plugins={[ChartDataLabels]}
      />
    </div>
  );
};

export default LineChart;
