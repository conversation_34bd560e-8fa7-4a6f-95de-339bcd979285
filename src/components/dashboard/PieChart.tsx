import { Chart as ChartJ<PERSON>, ArcElement, <PERSON><PERSON><PERSON>, <PERSON> } from "chart.js";
import { Pie } from "react-chartjs-2";

import ChartDataLabels from "chartjs-plugin-datalabels";
import { BoatType } from "@/models";

ChartJS.register(ArcE<PERSON>, Tooltip, Legend, ChartDataLabels);

interface Props {
  chartData: BoatType;
}

const PieChart = ({ chartData }: Props) => {
  const errorData = [
    chartData?.UNKNOWN,
    chartData?.QR_BBOX,
    chartData?.REF,
    chartData?.QR,
    chartData?.SHADOW,
    chartData?.STRIP,
    chartData?.NOSTRIP,
  ];
  const options = {
    plugins: {
      datalabels: {
        color: "#fff",
        font: { size: 16, family: "GilroyMedium" },
      },
    },
  };
  const data = {
    labels: ["UNKNOWN", "QR_BBOX", "REF", "QR", "SHADOW", "STRIP", "NOSTRIP"],
    datasets: [
      {
        data: errorData,
        backgroundColor: [
          "#FFE066",
          "#F8BCCE",
          "#E994D1",
          "#B485EF",
          "#8E92F4",
          "#A5BACF",
          "#ACE6D9",
        ],
        borderColor: ["rgba(255, 255, 255, 0.60)"],
        borderWidth: 2,
      },
    ],
  };

  return (
    <div style={{ width: "300px" }}>
      <Pie options={options} data={data} width="100%" height="200" />
    </div>
  );
};
export default PieChart;
