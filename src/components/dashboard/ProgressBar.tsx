import * as Progress from "@radix-ui/react-progress";
import styled from "@emotion/styled";
import { useEffect, useState } from "react";

interface Props {
  color: string;
  label: string;
  amount: number;
  total: number;
}

const ProgressBar = ({ color, label, amount, total }: Props) => {
  const [progress, setProgress] = useState(13);
  // console.log(total);
  useEffect(() => {
    const timer = setTimeout(() => setProgress((amount / total) * 100), 500);
    return () => clearTimeout(timer);
  }, [amount, total]);

  return (
    <Container>
      <LabelWrapper>
        <Label className="en">{label}</Label>
        <Amount>{amount}</Amount>
      </LabelWrapper>
      <Progress.Root
        style={{
          position: "relative",
          overflow: "hidden",
          background: "var(--color-bg-dark)",
          borderRadius: "99999px",
          width: "100%",
          height: "10px",
          transform: "translateZ(0)",
        }}
        value={isNaN(progress) ? null : progress}
      >
        <Progress.Indicator
          className="ProgressIndicator"
          style={{
            transform: `translateX(-${100 - progress}%)`,
            backgroundColor: color,
            width: "100%",
            height: "100%",
            transition: "transform 660ms cubic-bezier(0.65, 0, 0.35, 1)",
          }}
        />
      </Progress.Root>
    </Container>
  );
};

const Container = styled.div`
  padding-bottom: 10px;
  color: var(--color-text-dark);
`;

const LabelWrapper = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
`;

const Label = styled.div`
  margin-bottom: 5px;
`;

const Amount = styled.div`
  font-family: "GilroyMedium";
`;

export default ProgressBar;
