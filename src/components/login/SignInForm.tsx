import { useLoginMutation } from "@/apis/auth";
import { useForm } from "@/hooks";
import { SignInRequestType } from "@/models";
import type { KeyboardEvent } from "react";

const SignInForm = () => {
  const { state: loginInfo, handleChange } = useForm<SignInRequestType>({
    account: "",
    password: "",
  });
  const { mutate: loginRequestMutate } = useLoginMutation(loginInfo);

  const handleSubmit = (e: KeyboardEvent<HTMLFormElement>) => {
    e.preventDefault();
    loginRequestMutate();
  };

  return (
    <form action="/" onSubmit={handleSubmit}>
      <fieldset className="Fieldset">
        <input
          className="Input"
          id="name"
          type="text"
          name="account"
          value={loginInfo.account}
          onChange={handleChange}
          placeholder="ID"
        />
      </fieldset>
      <fieldset className="Fieldset">
        <input
          className="Input"
          id="password"
          type="password"
          name="password"
          value={loginInfo.password}
          onChange={handleChange}
          placeholder="PASSWORD"
        />
      </fieldset>
      <div>
        <button className="Button yellow">SIGNIN</button>
      </div>
    </form>
  );
};

export default SignInForm;
