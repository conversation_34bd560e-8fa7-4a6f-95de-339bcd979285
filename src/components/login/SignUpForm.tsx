import AuthApi from "@/apis/auth/index";
import { useForm } from "@/hooks";
import { SignUpRequestType } from "@/models";
import { isAxiosError } from "axios";
import { FormEvent } from "react";

const SignUpForm = () => {
  const { state: signUpInfo, handleChange } = useForm<SignUpRequestType>({
    account: "",
    password: "",
    name: "",
    signUpCode: "",
  });

  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    console.log(signUpInfo);
    e.preventDefault();
    signupRequest(signUpInfo);
  };

  const signupRequest = async (loginData: SignUpRequestType) => {
    try {
      const { data, status } = await AuthApi.signup(loginData);
      console.log(status, data);
      if (status === 201) {
        alert("회원가입이 완료되었습니다!\n 로그인 해주세요");
        window.location.reload();
      }
    } catch (error: unknown) {
      if (isAxiosError(error)) {
        console.error(error.response?.data.error.message);
        alert(error.response?.data.error.message);
      }
      // const { response } = error as unknown as AxiosError;
      // console.log(response);
    }
  };

  return (
    <form action="/" onSubmit={handleSubmit}>
      <fieldset className="Fieldset">
        <label className="Label" htmlFor="signupId"></label>
        <input
          className="Input"
          id="signupId"
          type="text"
          name="account"
          onChange={handleChange}
          placeholder="ID"
        />
      </fieldset>
      <fieldset className="Fieldset">
        <label className="Label" htmlFor="signupPw"></label>
        <input
          className="Input"
          id="signupPw"
          type="password"
          name="password"
          onChange={handleChange}
          placeholder="PASSWORD"
        />
      </fieldset>
      <fieldset className="Fieldset">
        <label className="Label" htmlFor="signupName"></label>
        <input
          className="Input"
          id="signupName"
          type="text"
          name="name"
          onChange={handleChange}
          placeholder="NAME"
        />
      </fieldset>
      <fieldset className="Fieldset">
        <label className="Label" htmlFor="signUpCode"></label>
        <input
          className="Input"
          id="signUpCode"
          type="text"
          onChange={handleChange}
          name="signUpCode"
          placeholder="SIGNUP CODE"
        />
      </fieldset>
      <div>
        <button className="Button yellow">SIGNUP</button>
      </div>
    </form>
  );
};

export default SignUpForm;
