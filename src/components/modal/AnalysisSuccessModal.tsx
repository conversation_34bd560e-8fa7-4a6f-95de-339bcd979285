import styled from "@emotion/styled";
import { ModalBackground, ModalSideNav } from ".";
import { Button, Textarea } from "..";
import { useAnimation, useInput } from "@/hooks";
import { useMemoChangeMutation } from "@/apis/analysis/useMemoChangeMutation";

interface Props {
  memoInitial: string | undefined;
  memoIdx?: number;
}

export const AnalysisSuccessModal = ({
  memoInitial = "",
  memoIdx = 1,
}: Props) => {
  console.log(memoInitial);
  const [state, handleChange] = useInput<string, HTMLTextAreaElement>(
    memoInitial
  );
  const { handleClose } = useAnimation("analysisSuccess");
  const { mutate: changeMemoMutate } = useMemoChangeMutation(state, memoIdx);

  return (
    <ModalBackground>
      <ModalSideNav modalName="analysisSuccess">
        <_Title>Memo</_Title>
        <_Subtitle>Memo</_Subtitle>
        <Textarea
          value={state}
          onChange={handleChange}
          placeholder="메모를 입력하세요."
          height="100px"
        />
        <_Container>
          <Button
            fontColor="#FFFFFF"
            buttonColor="#41d8e6"
            hoverColor="#d39e00"
            style={{ marginRight: "8px" }}
            onClick={() => changeMemoMutate()}
          >
            Save
          </Button>
          <Button
            fontColor="#41d8e6"
            buttonColor="#FFFFFF"
            onClick={() => handleClose()}
          >
            Cancel
          </Button>
        </_Container>
      </ModalSideNav>
    </ModalBackground>
  );
};

const _Title = styled.h2`
  font-size: 34px;
  margin-top: 2rem !important;
  font-weight: normal;
`;

const _Subtitle = styled.h4`
  font-size: 25px;
  color: #767676;
  font-weight: normal;
`;

const _Container = styled.div`
  margin-top: 30px;
  display: flex;
  justify-content: flex-end;
`;
