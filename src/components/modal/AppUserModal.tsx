import styled from "@emotion/styled";
import { useState } from "react";
import { capitalize } from "@/utils";
import { Button, ModalBackground, ModalSideNav } from "..";
import { useAnimation } from "@/hooks";
import { useRecoilValue } from "recoil";
import { statusAtom } from "@/atoms";
import { useChangeStatusMutation } from "@/apis/appUsers";

type StatusType = "Active" | "Archived";

export const AppUserModal = () => {
  const statusValue: StatusType[] = ["Active", "Archived"];
  const { handleClose } = useAnimation("appUser");
  const statusAtomState = useRecoilValue(statusAtom);
  const [status, setStatus] = useState<StatusType>(
    statusAtomState.status as StatusType
  );
  const { mutate: changeStatus } = useChangeStatusMutation(
    statusAtomState.accountId,
    status
  );

  return (
    <ModalBackground>
      <ModalSideNav modalName="appUser">
        <_TitleContainer>
          <_TitleLine />
          <_Title>Status</_Title>
        </_TitleContainer>
        <div>
          {statusValue.map((value: StatusType) => (
            <_StatusContainer key={value}>
              <_Radio
                type="radio"
                checked={status === value}
                value={value}
                name={value}
                onChange={(e) => setStatus(e.target.name as StatusType)}
              />
              <label htmlFor="check">{capitalize(value)}</label>
            </_StatusContainer>
          ))}
        </div>
        <_Container>
          <Button
            fontColor="#FFFFFF"
            buttonColor="#41d8e6"
            hoverColor="#d39e00"
            style={{ marginRight: "8px" }}
            onClick={() => changeStatus()}
          >
            Save
          </Button>
          <Button
            fontColor="#41d8e6"
            buttonColor="#FFFFFF"
            onClick={() => handleClose()}
          >
            Cancel
          </Button>
        </_Container>
      </ModalSideNav>
    </ModalBackground>
  );
};

const _TitleLine = styled.div`
  width: 3px;
  height: 1rem;
  background: #41d8e6;
  margin-right: 20px;
`;

const _TitleContainer = styled.div`
  width: 100%;
  margin-top: calc(70px + 1rem);
  display: flex;
  align-items: center;
`;

const _Title = styled.h4`
  color: #767676;
  font-size: 1.5rem;
  font-weight: 500;
  line-height: 1.2;
`;

const _Radio = styled.input`
  display: block;
  margin-right: 10px;
`;

const _StatusContainer = styled(_TitleContainer)`
  margin-top: 0;
`;

const _Container = styled.div`
  display: flex;
  justify-content: flex-end;
`;
