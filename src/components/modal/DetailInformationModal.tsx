import styled from "@emotion/styled";
import { Con<PERSON>er, ModalBackground, ModalSideNav, Image, CareChart } from "..";
import { useCareDetail } from "@/hooks";
import { useEffect, useState } from "react";
import { css } from "@emotion/react";
import { CymWeight, CymWater, CymUrine } from "@/assets";
import { useGetDetailCareInfoQuery } from "@/apis/appUsers";
import { getDateFormat } from "@/utils";

type ConditionType = "일" | "주" | "월" | "년";
type CareType = "urine" | "water" | "weight";

export const DetailInformationModal = () => {
  const [type] = useCareDetail();
  const condition: ConditionType[] = ["일", "주", "월", "년"];
  const careType: {
    [key: string]: CareType;
  } = {
    체중: "weight",
    수분: "water",
    배뇨: "urine",
  };
  const today: Date = new Date();
  const [conditionState, setConditionState] = useState<ConditionType>("일");
  const getNewDate = () => new Date();

  const startValue = {
    일: new Date(
      getNewDate().setDate(getNewDate().getDate() - 1)
    ).toISOString(),
    주: new Date(
      getNewDate().getFullYear(),
      getNewDate().getMonth(),
      getNewDate().getDate() - 7
    ).toISOString(),
    월: new Date(
      getNewDate().getFullYear(),
      getNewDate().getMonth() - 1,
      getNewDate().getDate()
    ).toISOString(),
    년: new Date(
      getNewDate().getFullYear() - 1,
      getNewDate().getMonth(),
      getNewDate().getDate()
    ).toISOString(),
  };

  const { data, refetch } = useGetDetailCareInfoQuery(careType[type], {
    start: startValue[conditionState],
    end: today.toISOString(),
    utcOffset: "+9",
    periodType: conditionState === "년" ? "year" : undefined,
  });
  const image: { [key: string]: string } = {
    체중: CymWeight,
    수분: CymWater,
    배뇨: CymUrine,
  };
  const img = image[type];

  const innerTitle = (num: number): string => {
    if (type === "체중") {
      return `최근 ${num}kg`;
    }

    if (type === "수분") {
      return `목표 물 섭취량 ${num.toLocaleString()}ml`;
    }

    return `오늘 배뇨 횟수 ${num}회`;
  };

  useEffect(() => {
    refetch();
  }, [conditionState, refetch]);

  return (
    <ModalBackground>
      <ModalSideNav modalName="detailInformation">
        <Container>
          <_TitleContainer>
            <_TitleLine />
            <_Title>{type} Care</_Title>
          </_TitleContainer>
          <div>
            {condition.map((text: ConditionType) => (
              <_Button
                key={text}
                text={text}
                condition={conditionState}
                onClick={() => setConditionState(text)}
              >
                {text}
              </_Button>
            ))}
          </div>
        </Container>
        {conditionState === "일" ? (
          <>
            <_Wrapper>
              <_LeftNav>{innerTitle(data?.average ?? 0)}</_LeftNav>
              <_Nav>
                {conditionState === "일"
                  ? getDateFormat(today.toISOString())
                  : `${getDateFormat(
                      startValue[conditionState]
                    )} ~ ${getDateFormat(today.toISOString())}`}
              </_Nav>
            </_Wrapper>
            <_Wrapper>
              <Image src={img} alt={type} />
              <span>{data?.recentWeight?.value}</span>
            </_Wrapper>
          </>
        ) : (
          <CareChart
            records={data?.records}
            montlyData={data?.monthlyData}
            condition={conditionState}
            target={
              type === "체중"
                ? (data?.subject.targetWeight as number)
                : type === "수분"
                ? (data?.subject.targetWater as number)
                : (data?.average as number)
            }
          />
        )}
        <_Wrapper>
          <_LeftNav>{type}기록</_LeftNav>
        </_Wrapper>
        {conditionState !== "년" ? (
          data ? (
            data.dailyData?.map((element) => (
              <_RecordList key={element.date}>
                <_Nav>{element.date}</_Nav>
                <_Nav>{element.value}</_Nav>
              </_RecordList>
            ))
          ) : (
            <>No Data</>
          )
        ) : data && data.monthlyData?.length !== 0 ? (
          data.monthlyData?.map((element) => (
            <_RecordList key={element.date}>
              <_Nav>{element.date}</_Nav>
              <_Nav>{element.value}</_Nav>
            </_RecordList>
          ))
        ) : (
          <>No Data</>
        )}
      </ModalSideNav>
    </ModalBackground>
  );
};

const _Wrapper = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  margin-top: 20px;
  padding: 0 0.6em;

  > img {
    margin-right: 30px;
  }

  > span {
    font-size: 2rem;
    font-weight: bold;
  }
`;

const _TitleLine = styled.div`
  width: 3px;
  height: 1rem;
  background: #41d8e6;
  margin-right: 10px;
`;

const _TitleContainer = styled.div`
  margin-top: 10px;
  display: flex;
  align-items: center;
`;

const _Title = styled.h4`
  color: #767676;
  font-size: 1.5rem;
  font-weight: 500;
  line-height: 1.2;
`;

const _Button = styled.button<{ condition: string; text: string }>`
  width: 30px;
  height: 30px;
  padding: 1px 6px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 17px;
  margin-right: 5px;
  color: #ffffff;
  ${({ condition, text }) => {
    if (condition === text) {
      return css`
        background: #41d8e6;
        box-shadow: 0 3px 5px #333 !important;
      `;
    }
    return css`
      background: #808080;
    `;
  }}

  :hover {
    background: #41d8e6;
  }
`;

const _Nav = styled.div`
  width: 50%;
  color: #767676;
`;

const _LeftNav = styled(_Nav)`
  font-size: 21px;
  font-weight: 500;
`;

const _RecordList = styled.div`
  width: 100%;
  margin-top: 20px;
  padding: 0 0.6em;
  display: flex;
`;
