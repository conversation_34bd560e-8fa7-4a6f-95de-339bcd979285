import {
  ModalBackground,
  ModalSideNav,
  Image,
  BasicInformation,
  CareInformation,
  AnalysisInformation,
  ImageModal,
  DetailInformationModal,
  DietRecordModal,
} from "@/components";
import styled from "@emotion/styled";
import { CymBasic } from "@/assets";
import { useState } from "react";
import { css } from "@emotion/react";
import { useModal } from "@/hooks";
import { useRecoilState, useRecoilValue, useSetRecoilState } from "recoil";
import { accountAtom, profileAtom, subjectIdAtom } from "@/atoms";
import { useGetDetailUserQuery } from "@/apis/appUsers";

type MenuType = "Basic" | "Care" | "Analysis";

export const DetailProfileModal = () => {
  const menu: MenuType[] = [
    "Basic",
    // "Care",
    "Analysis",
  ];
  const [profileIndexState, setProfileIndexState] =
    useRecoilState<number>(profileAtom);
  const setSubjectIdState = useSetRecoilState<number | string>(subjectIdAtom);
  const [menuState, setMenuState] = useState<MenuType>("Basic");
  const { modalState: cropModalState } = useModal("crop");
  const { modalState: maskModalState } = useModal("mask");
  const { modalState: detailModalState } = useModal("detailInformation");
  const { modalState: dietRecordModalState } = useModal("dietRecord");
  const accountId = useRecoilValue(accountAtom);
  const { data } = useGetDetailUserQuery(accountId);
  const COMPONENT_MAP = {
    Basic: BasicInformation,
    // Health: HealthInformation,
    Care: CareInformation,
    // Solution: SolutionInformation,
    Analysis: AnalysisInformation,
  };
  const Component = COMPONENT_MAP[menuState as keyof typeof COMPONENT_MAP];

  const handleChange = (text: MenuType) => {
    setMenuState(text);
  };

  if (dietRecordModalState.isOpen) {
    return <DietRecordModal />;
  }

  if (detailModalState.isOpen) {
    return <DetailInformationModal />;
  }

  if (cropModalState.isOpen) {
    return <ImageModal title="crop image" modalKey="crop" />;
  }

  if (maskModalState.isOpen) {
    return <ImageModal title="mask image" modalKey="mask" />;
  }

  return (
    <ModalBackground>
      <ModalSideNav modalName="detailProfile" width="1000px">
        <_ProfileWrapper>
          {data?.subjects
            ?.filter((value) => value.status === "active")
            .map((item) => (
              <_Wrapper
                key={item.id}
                selected={profileIndexState === item.id}
                onClick={() => {
                  setProfileIndexState(item.id);
                  setSubjectIdState(item.id);
                  setMenuState("Basic");
                }}
              >
                <Image
                  src={item.image ?? CymBasic}
                  alt={`${item.nickname} profile image`}
                  width="100px"
                  height="100px"
                />
                <_Name>{item.nickname}</_Name>
              </_Wrapper>
            ))}
        </_ProfileWrapper>
        <_FilterWrapper>
          {menu.map((text: MenuType) => (
            <_FilterButton
              text={text}
              nowState={menuState}
              key={text}
              onClick={() => handleChange(text)}
            >
              {text}
            </_FilterButton>
          ))}
        </_FilterWrapper>
        {Component && <Component />}
      </ModalSideNav>
    </ModalBackground>
  );
};

const _Wrapper = styled.div<{ selected?: boolean }>`
  width: fit-content;
  display: flex;
  flex-direction: column;
  align-items: center;

  > img {
    ${({ selected }) => selected && "box-shadow: 0px 0px 12px 0px #00ffff;"}
    border-radius: 50%;
    border: 2px solid #cccccc;
  }
`;

const _Name = styled.span`
  margin-top: 10px;
  font-size: 13px;
  text-align: center;
`;

const _FilterWrapper = styled.div`
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  border-bottom: 1px solid #c5c5c5;
`;

const _FilterButton = styled.button<{ text: string; nowState: string }>`
  cursor: pointer;
  padding: 0 1rem;
  background: transparent;
  font-size: 1.25rem;
  // font-family: "Noto Sans KR", sans-serif;

  ${({ text, nowState }) =>
    text === nowState
      ? css`
          border-bottom: 2px solid #394853;
          color: #394853;
        `
      : css`
          color: #cccccc;
        `};
`;

const _ProfileWrapper = styled.div`
  width: 100%;
  display: flex;
`;
