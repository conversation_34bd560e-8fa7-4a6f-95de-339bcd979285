import { ModalBackground, ModalSideNav } from "..";
import styled from "@emotion/styled";
import Calendar from "react-calendar";
import { useState, useEffect } from "react";
import { getDay, timeFormat } from "@/utils";
import { useGetFoodRecordsQuery } from "@/apis/appUsers";
import { useRecoilValue } from "recoil";
import { statusAtom } from "@/atoms";

type ValuePiece = Date | null;
type Value = ValuePiece | [ValuePiece, ValuePiece];

export const DietRecordModal = () => {
  const statusState = useRecoilValue(statusAtom);
  const [date, setDate] = useState<Value>(new Date());
  const { data, refetch } = useGetFoodRecordsQuery(
    date as Date,
    date as Date,
    statusState.status as "Active" | "Deleted"
  );

  useEffect(() => {
    refetch();
  }, [date, refetch]);

  return (
    <ModalBackground>
      <ModalSideNav modalName="dietRecord" width={700}>
        <_TitleContainer>
          <_TitleLine />
          <_Title>식단 기록</_Title>
        </_TitleContainer>
        <_Wrapper>
          <span>{getDay(date)}</span>
          <Calendar onChange={setDate} value={date} />
        </_Wrapper>
        <_TitleContainer>
          <_TitleLine />
          <_Title>아침</_Title>
        </_TitleContainer>
        {data?.breakfasts?.map((item) => (
          <_Wrapper key={item.id}>
            <span>{item.food.korean}</span>
            <span>{timeFormat(item.createdAt)}</span>
            <span>{item.food.calorie.toFixed(1)}kcal</span>
          </_Wrapper>
        ))}
        <_TitleContainer>
          <_TitleLine />
          <_Title>점심</_Title>
        </_TitleContainer>
        {data?.lunches?.map((item) => (
          <_Wrapper key={item.id}>
            <span>{item.food.korean}</span>
            <span>{timeFormat(item.createdAt)}</span>
            <span>{item.food.calorie.toFixed(1)}kcal</span>
          </_Wrapper>
        ))}
        <_TitleContainer>
          <_TitleLine />
          <_Title>저녁</_Title>
        </_TitleContainer>
        {data?.dinners?.map((item) => (
          <_Wrapper key={item.id}>
            <span>{item.food.korean}</span>
            <span>{timeFormat(item.createdAt)}</span>
            <span>{item.food.calorie.toFixed(1)}kcal</span>
          </_Wrapper>
        ))}
        <_TitleContainer>
          <_TitleLine />
          <_Title>간식</_Title>
        </_TitleContainer>
        {data?.snacks?.map((item) => (
          <_Wrapper key={item.id}>
            <span>{item.food.korean}</span>
            <span>{timeFormat(item.createdAt)}</span>
            <span>{item.food.calorie.toFixed(1)}kcal</span>
          </_Wrapper>
        ))}
      </ModalSideNav>
    </ModalBackground>
  );
};

const _TitleLine = styled.div`
  width: 3px;
  height: 1rem;
  background: #41d8e6;
  margin-right: 10px;
`;

const _TitleContainer = styled.div`
  width: 100%;
  margin-top: 10px;
  display: flex;
  align-items: center;
`;

const _Title = styled.h4`
  color: #767676;
  font-size: 1.5rem;
  font-weight: 500;
  line-height: 1.2;
`;

const _Wrapper = styled.div`
  width: 100%;
  display: flex;
  justify-content: space-between;
`;
