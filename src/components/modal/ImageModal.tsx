import { useImage, useModal } from "@/hooks";
import styled from "@emotion/styled";
import { XSvg } from "@/assets";
import { ModalBackground } from "..";
import ReactImageMagnify from "react-image-magnify";

interface ImageModalProps {
  title: string;
  modalKey: string;
}

export const ImageModal = ({ title, modalKey }: ImageModalProps) => {
  const { closeModal } = useModal(modalKey);
  const [imageUrl] = useImage();

  return (
    <ModalBackground center>
      <_Modal>
        <_ModalHeader>
          <h5>{title}</h5>
          <XSvg width={30} height={30} onClick={closeModal} />
        </_ModalHeader>
        <_ModalBody>
          <ReactImageMagnify
            {...{
              smallImage: {
                alt: `${title} image url`,
                isFluidWidth: false,
                src: imageUrl,
                width: 300,
                height: 544,
              },
              largeImage: {
                src: imageUrl,
                width: 300 * 2,
                height: 544 * 2,
              },
            }}
          />
        </_ModalBody>
      </_Modal>
    </ModalBackground>
  );
};

const _Modal = styled.div`
  width: 350px;
  min-width: 300px;
  height: 658px;
  background: #ffffff;
  border-radius: 15px;
`;

const _ModalHeader = styled.div`
  width: 100%;
  height: 10%;
  padding: 1rem;
  color: #ffffff;
  background-color: #41d8e6;
  border-radius: 15px 15px 0 0;
  display: flex;
  justify-content: space-between;
  font-size: 24px;
  align-items: center;
`;

const _ModalBody = styled.div`
  width: 100%;
  height: 90%;
  padding: 1.5rem;
  display: flex;
  justify-content: center;
  align-items: center;

  > div {
    height: 100%;
  }
  > image {
    width: 300px;
    height: auto;
    object-fit: fill;
  }
`;
