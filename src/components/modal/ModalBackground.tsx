import { css } from "@emotion/react";
import styled from "@emotion/styled";
import { ReactNode, HTMLAttributes } from "react";

interface Props extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
  center?: boolean;
}

export const ModalBackground = ({ children, center, ...rest }: Props) => {
  return (
    <_Background center={center} {...rest}>
      {children}
    </_Background>
  );
};

const _Background = styled.div<{ center?: boolean }>`
  width: 100vw;
  height: 100vh;
  display: flex;
  ${({ center }) =>
    center
      ? css`
          justify-content: center;
          align-items: center;
        `
      : css`
          justify-content: flex-end;
          align-items: flex-end;
        `}
  background: rgba(0, 0, 0, 0.6);
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
  z-index: 2;
`;
