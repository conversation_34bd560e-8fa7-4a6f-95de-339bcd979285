import styled from "@emotion/styled";
import { ReactNode } from "react";
import { XSvg } from "@/assets";
import { useAnimation } from "@/hooks";
import { Keyframes } from "@emotion/react";

interface Props {
  children: ReactNode;
  modalName: string;
  width?: number | string;
}

export const ModalSideNav = ({ children, modalName, width = "600px" }: Props) => {
  const { animation, handleClose } = useAnimation(modalName);

  return (
    <_ModalSideNav width={width} animation={animation.animation}>
      <XSvg onClick={() => handleClose()} width={"1.25rem"} height={"1.25rem"} alignSelf="flex-end" />
      {children}
    </_ModalSideNav>
  );
};

const _ModalSideNav = styled.div<{
  animation: Keyframes;
  width: number | string;
}>`
  width: ${({ width }) => (typeof width === "string" ? `${width}` : `${width}px`)};
  height: calc(100vh - 90px);
  background: var(--color-submenu-bg-dark);
  animation: ${({ animation }) => animation} 0.4s ease-in-out forwards;
  padding: 25px 1rem;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  box-shadow: 1px 2px 6px rgba(0, 0, 0, 0.1);
  overflow-y: scroll;
`;
