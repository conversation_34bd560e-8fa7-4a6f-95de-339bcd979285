import styled from "@emotion/styled";
import { Mo<PERSON>Background, ModalSideNav, Input, Button } from "..";
import { useAnimation, useForm, useSelectUser } from "@/hooks";
import { usePatchAdminMutation } from "@/apis/admin";

export const UserEditModal = () => {
  const { handleClose } = useAnimation("userEdit");
  const { state, handleChange } = useForm({
    name: "",
    password: "",
    confirmPassword: "",
  });
  const [user] = useSelectUser();
  const { mutate: changeUserInfo } = usePatchAdminMutation(user?.id as number);

  const handleClick = () => {
    if (state.password === state.confirmPassword) {
      changeUserInfo({ name: state.name, password: state.password });
      return;
    }

    alert("비밀번호가 일치하지 않습니다. 한번 더 확인해주세요.");
  };

  return (
    <ModalBackground>
      <ModalSideNav modalName="userEdit">
        <_TitleContainer>
          <_TitleLine />
          User
        </_TitleContainer>
        <_Container>
          <_SubInputHeading>ID</_SubInputHeading>
          <Input placeholder="ID" value={user?.id} disabled />
          <_SubInputHeading>Account</_SubInputHeading>
          <Input placeholder="Account" value={user?.account} disabled />
          <_SubInputHeading>Name</_SubInputHeading>
          <Input
            placeholder="Name"
            onChange={handleChange}
            name="name"
            value={state.name}
          />
          <_SubInputHeading>Password</_SubInputHeading>
          <Input
            placeholder="Password"
            type="password"
            onChange={handleChange}
            name="password"
            value={state.password}
          />
          <_SubInputHeading>Confirm Password</_SubInputHeading>
          <Input
            placeholder="Confirm Password"
            type="password"
            onChange={handleChange}
            name="confirmPassword"
            value={state.confirmPassword}
          />
        </_Container>
        <_ButtonWrapper>
          <_Button
            padding="0.5rem 1rem"
            buttonColor="#41d8e6"
            fontColor="#FFF"
            onClick={handleClick}
          >
            Save
          </_Button>
          <Button onClick={() => handleClose()}>Cancel</Button>
        </_ButtonWrapper>
      </ModalSideNav>
    </ModalBackground>
  );
};

const _TitleContainer = styled.h4`
  width: 100%;
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  color: #767676;
  font-weight: 500;
  line-height: 1.2;
  margin-bottom: 1rem;
`;

const _TitleLine = styled.div`
  width: 3px;
  height: 1rem;
  background: #41d8e6;
  margin-right: 20px;
`;

const _Container = styled.div`
  display: flex;
  flex-direction: column;
`;

const _SubInputHeading = styled.label`
  font-size: 17px;
  margin: 0.5rem 0;
`;

const _ButtonWrapper = styled.div`
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
`;

const _Button = styled(Button)`
  margin-right: 1rem;
`;
