import styled from "@emotion/styled";
import { css } from "@emotion/react";
import { useStatistics } from "@/hooks";

export const StatisticFilter = () => {
  const menu: string[] = ["가입자", "사용자(애완동물)", "검사", "탈퇴사유"];
  const [state, handleChange] = useStatistics();

  return (
    <_FilterWrapper>
      {menu.map((text: string) => (
        <_FilterButton
          text={text}
          nowState={state}
          key={text}
          onClick={() => handleChange(text)}
        >
          {text}
        </_FilterButton>
      ))}
    </_FilterWrapper>
  );
};

const _FilterWrapper = styled.div`
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  border-bottom: 1px solid #c5c5c5;
`;

const _FilterButton = styled.button<{ text: string; nowState: string }>`
  cursor: pointer;
  padding: 0 1rem;
  background: transparent;
  font-size: 1.25rem;
  font-family: "Noto Sans KR", sans-serif;

  ${({ text, nowState }) =>
    text === nowState
      ? css`
          border-bottom: 2px solid #394853;
          color: #394853;
        `
      : css`
          color: #cccccc;
        `};
`;
