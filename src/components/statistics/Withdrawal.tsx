import { useForm } from "@/hooks";
import { OpinionsRequestType } from "@/models";
import { getDateFormat } from "@/utils";
import {
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TablePagination,
} from "@mui/material";
import { useGetWithdrawalsQuery } from "@/apis/statistics";
import { ChangeEvent, useEffect } from "react";
import { RowsPerPageOptions } from "@/constants";

export const Withdrawal = () => {
  const { state, setState } = useForm<OpinionsRequestType>({
    limit: 15,
    page: 1,
    sort: "createdAt",
    order: "DESC",
  });
  const { data, refetch } = useGetWithdrawalsQuery(state);

  useEffect(() => {
    refetch();
  }, [refetch, state.page, state.limit]);

  return (
    <TableContainer>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>ID</TableCell>
            <TableCell>Account ID</TableCell>
            <TableCell>Comment</TableCell>
            <TableCell>Created</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {data?.opinions.map((item, idx) => (
            <TableRow
              key={idx}
              tabIndex={-1}
              sx={{
                ":hover": { background: "#ededed" },
                "&:last-child td, &:last-child th": { border: 0 },
                ":focus": {
                  background: "#fffa90",
                },
              }}
            >
              <TableCell sx={{ border: 0 }}>{item.id}</TableCell>
              <TableCell sx={{ border: 0 }}>{item.accountId}</TableCell>
              <TableCell sx={{ border: 0 }}>{item.comment}</TableCell>
              <TableCell sx={{ border: 0 }}>
                {getDateFormat(item.createdAt)}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <TablePagination
        component="div"
        count={data?.count ?? 0}
        page={(state.page as number) - 1}
        rowsPerPageOptions={RowsPerPageOptions}
        rowsPerPage={state.limit || 1}
        onRowsPerPageChange={(e: ChangeEvent<HTMLInputElement>) => {
          const { value } = e.target;

          setState((prev) => ({ ...prev, limit: Number(value) }));
        }}
        onPageChange={(_, num) =>
          setState((prev) => ({ ...prev, page: num + 1 }))
        }
      />
    </TableContainer>
  );
};
