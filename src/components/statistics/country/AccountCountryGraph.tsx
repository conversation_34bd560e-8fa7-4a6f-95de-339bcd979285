import { useGetAccountCountryQuery } from "@/apis/statistics";
import styled from "@emotion/styled";
import { Bar } from "react-chartjs-2";
import {
  Chart,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import ChartDataLabels from "chartjs-plugin-datalabels";
import { useCountryNumber } from "@/hooks";

Chart.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartDataLabels
);

export const AccountCountryGraph = () => {
  const { data } = useGetAccountCountryQuery();
  const { findCountryNameByCode } = useCountryNumber();

  return (
    <_Wrapper>
      <Bar
        data={{
          labels: data?.map(({ countryNumber }) =>
            findCountryNameByCode(countryNumber as `+${number}`)
          ),
          datasets: [
            {
              data: data?.map(({ count }) => count),
              backgroundColor: "rgb(161,208,245, 0.7)",
            },
          ],
        }}
        options={{
          plugins: {
            legend: {
              display: false,
            },
          },
        }}
        width={1020}
        height={450}
      />
    </_Wrapper>
  );
};

const _Wrapper = styled.div`
  width: 1020px;
  height: 450px;
`;
