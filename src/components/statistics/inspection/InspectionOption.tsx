import styled from "@emotion/styled";
import { useInput } from "@/hooks";
import {
  MonthInspectionGraph,
  Input,
  AgeInspectionGraph,
  HoursInspectGraph,
  AccountCountryGraph,
} from "@/components";

type StateType = "months" | "age" | "time" | "accountCountry";

export const InspectionOption = () => {
  const [state, handleChange] = useInput<StateType, HTMLInputElement>("months");
  const option = [
    {
      label: "월별",
      value: "months",
    },
    {
      label: "연령대별",
      value: "age",
    },
    {
      label: "시간대별",
      value: "time",
    },
    {
      label: "국가별",
      value: "accountCountry",
    },
  ] as const;

  return (
    <>
      <_Wrapper>
        {option.map((item) => (
          <_Label key={item.value}>
            <Input
              type="radio"
              name={item.label}
              value={item.value}
              checked={state === item.value}
              onChange={handleChange}
            />
            {item.label}
          </_Label>
        ))}
      </_Wrapper>
      {state === "months" && <MonthInspectionGraph />}
      {state === "age" && <AgeInspectionGraph />}
      {state === "time" && <HoursInspectGraph />}
      {state === "accountCountry" && <AccountCountryGraph />}
    </>
  );
};

const _Wrapper = styled.div`
  width: 100%;
  display: flex;
  padding: 10px 0;
`;

const _Label = styled.label`
  margin-right: 1rem;
  display: flex;
  align-items: center;
  cursor: pointer;
`;
