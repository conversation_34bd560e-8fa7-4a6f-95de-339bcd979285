import { useGetMonthlyAnalysisQuery } from "@/apis/statistics";
import styled from "@emotion/styled";
import {
  Chart,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import ChartDataLabels from "chartjs-plugin-datalabels";
import { Bar } from "react-chartjs-2";

Chart.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartDataLabels
);

export const MonthInspectionGraph = () => {
  const { data } = useGetMonthlyAnalysisQuery(new Date().toISOString());

  return (
    <_Wrapper>
      <Bar
        data={{
          labels: data?.map((item) => item.month),
          datasets: [
            {
              label: "Male",
              data: data?.map((item) => item.maleCount),
              backgroundColor: "rgb(161,208,245, 0.7)",
            },
            {
              label: "Female",
              data: data?.map((item) => item.femaleCount),
              backgroundColor: "rgb(255,178,193, 0.7)",
            },
          ],
        }}
        width={1020}
        height={450}
      />
    </_Wrapper>
  );
};

const _Wrapper = styled.div`
  width: 1020px;
  height: 450px;
`;
