import { useGetMonthlyRegisterQuery } from "@/apis/statistics";
import styled from "@emotion/styled";
import {
  Chart,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import ChartDataLabels from "chartjs-plugin-datalabels";
import { Bar } from "react-chartjs-2";

Chart.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartDataLabels
);

export const MonthRegister = () => {
  const currentDate = new Date();
  const oneYearAgoDate = new Date(
    currentDate.getFullYear() - 1,
    currentDate.getMonth(),
    currentDate.getDate()
  );
  const { data } = useGetMonthlyRegisterQuery({
    start: oneYearAgoDate.toISOString(),
    end: currentDate.toISOString(),
  });

  return (
    <_Wrapper>
      <Bar
        data={{
          labels: data?.map((item) => item.month),
          datasets: [
            {
              data: data?.map((item) => item.count),
              backgroundColor: "rgba(161, 208, 245, 0.7)",
            },
          ],
        }}
        width={1020}
        height={450}
        options={{
          plugins: {
            legend: {
              display: false,
            },
          },
        }}
      />
    </_Wrapper>
  );
};

const _Wrapper = styled.div`
  width: 1020px;
  height: 450px;
`;
