import styled from "@emotion/styled";
import { CountryRegister, Input, MonthRegister } from "@/components";
import { useInput } from "@/hooks";

export const RegisterOption = () => {
  const [state, handleChange] = useInput<string, HTMLInputElement>("country");
  const option = [
    {
      label: "나라별",
      value: "country",
    },
    {
      label: "월별",
      value: "months",
    },
  ];

  return (
    <>
      <_Wrapper>
        {option.map((item) => (
          <_Label key={item.value}>
            <Input
              type="radio"
              name={item.label}
              value={item.value}
              checked={state === item.value}
              onChange={handleChange}
            />
            {item.label}
          </_Label>
        ))}
      </_Wrapper>
      {state === "country" && <CountryRegister />}
      {state === "months" && <MonthRegister />}
    </>
  );
};

const _Wrapper = styled.div`
  width: 100%;
  display: flex;
  padding: 10px 0;
`;

const _Label = styled.label`
  margin-right: 1rem;
  display: flex;
  align-items: center;
  cursor: pointer;
`;
