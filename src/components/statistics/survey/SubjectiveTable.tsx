import { useGetTextAnswerQuery } from "@/apis/statistics/useGetTextAnswerQuery";
import { subjectiveIdAtom } from "@/atoms";
import { useForm } from "@/hooks";
import { TextAnswserRequestType } from "@/models";
import { getDateFormat } from "@/utils";
import {
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TablePagination,
} from "@mui/material";
import { useEffect } from "react";
import { useRecoilValue } from "recoil";

export const SubjectiveTable = () => {
  const subjectiveId = useRecoilValue<1 | 3 | 9>(subjectiveIdAtom);
  const { state, setState } = useForm<TextAnswserRequestType>({
    limit: 15,
    page: 1,
    sort: "createdAt",
    order: "DESC",
  });
  const { data, refetch } = useGetTextAnswerQuery({
    ...state,
    questionId: subjectiveId,
  });

  useEffect(() => {
    refetch();
  }, [subjectiveId, refetch]);

  return (
    <TableContainer>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Subject ID</TableCell>
            <TableCell>Text</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Created</TableCell>
            <TableCell>Updated</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {data?.textAnswers.map((item) => (
            <TableRow
              key={item.id}
              tabIndex={-1}
              sx={{
                ":hover": { background: "#ededed" },
                "&:last-child td, &:last-child th": { border: 0 },
                ":focus": {
                  background: "#fffa90",
                },
              }}
            >
              <TableCell
                sx={{
                  border: 0,
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                }}
              >
                {item.subjectId}
              </TableCell>
              <TableCell
                sx={{
                  border: 0,
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                }}
              >
                {item.text}
              </TableCell>
              <TableCell
                sx={{
                  border: 0,
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                }}
              >
                {item.status}
              </TableCell>
              <TableCell
                sx={{
                  border: 0,
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                }}
              >
                {getDateFormat(item.createdAt)}
              </TableCell>
              <TableCell sx={{ border: 0 }}>
                {getDateFormat(item.updatedAt)}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <TablePagination
        component="div"
        count={data?.count ?? 0}
        page={(state.page as number) - 1}
        rowsPerPage={10}
        onPageChange={(_, num) =>
          setState((prev) => ({ ...prev, page: num + 1 }))
        }
      />
    </TableContainer>
  );
};
