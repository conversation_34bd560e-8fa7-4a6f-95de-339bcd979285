import styled from "@emotion/styled";
import { useInput } from "@/hooks";
import {
  AppUsagePurposeGraph,
  Input,
  UsageEatGraph,
  UsageExerciseGraph,
  Select,
  SubjectiveTable,
} from "@/components";
import {
  ChoiceIdOptions,
  QuestionIdOptions,
  TextAnswerOptions,
} from "@/constants";
import { useRecoilState } from "recoil";
import { SolutionDiseasesAtom, subjectiveIdAtom } from "@/atoms";
import { SolutionDiseasesRequestType } from "@/models";
import { ChangeEvent } from "react";

export const SurveyOption = () => {
  const [option, handleOptionChange] = useInput<string, HTMLInputElement>(
    "usagePurpose"
  );
  const [subjectiveId, setSubjectiveId] = useRecoilState<1 | 3 | 9>(
    subjectiveIdAtom
  );
  const [solutionDiseases, setSolutionDiseases] =
    useRecoilState<SolutionDiseasesRequestType>(SolutionDiseasesAtom);
  const options = [
    {
      label: "앱 사용 목적",
      value: "usagePurpose",
    },
    {
      label: "평소 운동량",
      value: "usageExercise",
    },
    {
      label: "식생활",
      value: "eat",
    },
    {
      label: "주관식",
      value: "subjective",
    },
  ];

  const handleChange = (e: ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;

    setSolutionDiseases((prev) => ({
      ...prev,
      [name]: Number(value),
    }));
  };

  return (
    <>
      <_Wrapper>
        {options.map((item) => (
          <_Label key={item.value}>
            <Input
              type="radio"
              name={item.label}
              value={item.value}
              checked={option === item.value}
              onChange={handleOptionChange}
            />
            {item.label}
          </_Label>
        ))}
        {option === "eat" && (
          <>
            <Select
              options={ChoiceIdOptions}
              value={solutionDiseases.choiceId}
              name="choiceId"
              onChange={handleChange}
              showArrow
            />
            <Select
              options={QuestionIdOptions}
              value={solutionDiseases.questionId}
              name="questionId"
              onChange={handleChange}
              showArrow
            />
          </>
        )}
        {option === "subjective" && (
          <Select
            options={TextAnswerOptions}
            value={subjectiveId}
            onChange={(e) => {
              setSubjectiveId(parseInt(e.target.value) as 1 | 3 | 9);
            }}
            showArrow
          />
        )}
      </_Wrapper>
      {option === "usagePurpose" && <AppUsagePurposeGraph />}
      {option === "usageExercise" && <UsageExerciseGraph />}
      {option === "eat" && <UsageEatGraph />}
      {option === "subjective" && <SubjectiveTable />}
    </>
  );
};

const _Wrapper = styled.div`
  width: 100%;
  display: flex;
  padding: 10px 0;
`;

const _Label = styled.label`
  margin-right: 1rem;
  display: flex;
  align-items: center;
  cursor: pointer;
`;
