import { Pie } from "react-chartjs-2";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ArcElement, Too<PERSON><PERSON> } from "chart.js";
import styled from "@emotion/styled";
import { useGetSolutionDiseasesQuery } from "@/apis/statistics";
import { useRecoilValue } from "recoil";
import { SolutionDiseasesAtom } from "@/atoms";
import { useEffect } from "react";

Chart.register(PieController, ArcElement, Tooltip);

export const UsageEatGraph = () => {
  const solutionDiseases = useRecoilValue(SolutionDiseasesAtom);
  const { data, refetch } = useGetSolutionDiseasesQuery(solutionDiseases);
  const colors: string[] = [
    "rgb(55,162,235)",
    "rgb(254,100,132)",
    "rgb(255,159,65)",
    "rgb(255,204,86)",
  ];
  const backgroundColor = data?.answerCounts.map(
    (_, idx) => colors[idx % colors.length]
  ) as string[];

  useEffect(() => {
    console.log(solutionDiseases);
  }, [solutionDiseases, refetch]);

  return (
    <_Wrapper>
      {data?.answerCounts && (
        <Pie
          data={{
            labels: data?.answerCounts.map((item) => item.answer),
            datasets: [
              {
                data: data?.answerCounts.map((item) => item.count),
                backgroundColor: backgroundColor,
              },
            ],
          }}
          options={{
            plugins: {
              tooltip: {
                callbacks: {
                  label: (context) => {
                    let label: string = context.label || "";
                    const total: number = data?.answerCounts.reduce(
                      (acc, cur) => acc + cur.count,
                      0
                    ) as number;

                    if (label) label += ": ";

                    const value: number = context.parsed;

                    if (value !== null)
                      label += ((value / total) * 100).toFixed(2) + "%";

                    return label;
                  },
                },
              },
              datalabels: {
                color: "black",
                align: "end",
                anchor: "center",
                formatter: (value: number) => {
                  const total = data?.answerCounts.reduce(
                    (acc, cur) => acc + cur.count,
                    0
                  ) as number;

                  if (value === 0) return "";

                  return `${value} (${((value / total) * 100).toFixed(2)}%)`;
                },
              },
            },
          }}
          width={1020}
          height={450}
        />
      )}
    </_Wrapper>
  );
};

const _Wrapper = styled.div`
  width: 1020px;
  height: 450px;
`;
