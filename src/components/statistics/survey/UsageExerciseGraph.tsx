import {
  Chart,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import styled from "@emotion/styled";
import ChartDataLabels from "chartjs-plugin-datalabels";
import { Bar } from "react-chartjs-2";
import { useGetSurveyAnswerQuery } from "@/apis/statistics";

Chart.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartDataLabels
);

export const UsageExerciseGraph = () => {
  const { data } = useGetSurveyAnswerQuery(2);

  const introduceFormatting = (text: string) => {
    const str: string[] = [];

    str.push(`${text[0]} : `);

    for (let i = 3, len = text.length; i < len - 1; i++) {
      str.push(text[i]);
    }

    return str;
  };

  return (
    <_Wrapper>
      <Bar
        data={{
          labels: ["상", "중", "하", "거의 하지 않음"],
          datasets: [
            {
              data: data?.answerCounts.map((item) => item.count),
              backgroundColor: "rgba(161, 208, 245, 0.7)",
            },
          ],
        }}
        options={{
          plugins: {
            legend: {
              display: false,
            },
            datalabels: {
              align: "end",
              anchor: "center",
              formatter: (value: number) => {
                if (value === 0) return "";

                return value;
              },
            },
          },
          scales: {
            y: {
              beginAtZero: true,
            },
          },
        }}
        width={1020}
        height={450}
      />
      <_Ul>
        {data?.answerCounts.map((item) => {
          if (item.answer === "거의 하지 않음") return;
          
          return <li key={item.answer}>{introduceFormatting(item.answer)}</li>;
        })}
      </_Ul>
    </_Wrapper>
  );
};

const _Wrapper = styled.div`
  width: 1020px;
  height: 450px;
`;

const _Ul = styled.ul`
  margin-top: 1rem;
  list-style-type: none;

  > li {
    padding: 1rem;
    list-style-type: "disc";

    ::after {
      content: "";
    }
  }
`;
