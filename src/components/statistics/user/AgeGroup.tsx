import {
  Chart,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Bar } from "react-chartjs-2";
import ChartDataLabels from "chartjs-plugin-datalabels";
import styled from "@emotion/styled";
import { useGetAgeGroupQuery } from "@/apis/statistics";

Chart.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartDataLabels
);

export const AgeGroup = () => {
  const { data } = useGetAgeGroupQuery();

  return (
    <_Wrapper>
      <Bar
        data={{
          labels: data?.map(({ ageGroup }) =>
            ageGroup < 10 ? "10대 미만" : `${ageGroup}대`
          ),
          datasets: [
            {
              data: data?.map(({ count }) => count),
              backgroundColor: "rgba(161, 208, 245, 0.7)",
            },
          ],
        }}
        options={{
          plugins: {
            legend: {
              display: false,
            },
            datalabels: {
              align: "end",
              anchor: "center",
              formatter: (value: number) => {
                if (value === 0) return "";

                return value;
              },
            },
          },
          scales: {
            y: {
              beginAtZero: true,
            },
          },
        }}
        width={1020}
        height={450}
      />
    </_Wrapper>
  );
};

const _Wrapper = styled.div`
  width: 1020px;
  height: 450px;
`;
