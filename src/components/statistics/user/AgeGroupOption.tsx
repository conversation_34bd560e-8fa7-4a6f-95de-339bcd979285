import { AgeGroup, Input } from "@/components";
import { useInput } from "@/hooks";
import styled from "@emotion/styled";

export const AgeGroupOption = () => {
  const [state, handleChange] = useInput<string, HTMLInputElement>("age");
  const option = [
    {
      label: "연령대별",
      value: "age",
    },
  ];

  return (
    <>
      <_Wrapper>
        {option.map((item) => (
          <_Label key={item.value}>
            <Input
              type="radio"
              name={item.label}
              value={item.value}
              checked={state === item.value}
              onChange={handleChange}
            />
            {item.label}
          </_Label>
        ))}
      </_Wrapper>
      {state === "age" && <AgeGroup />}
    </>
  );
};

const _Wrapper = styled.div`
  width: 100%;
  display: flex;
  padding: 10px 0;
`;

const _Label = styled.label`
  margin-right: 1rem;
  display: flex;
  align-items: center;
  cursor: pointer;
`;
