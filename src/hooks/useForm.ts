import {
  useState,
  ChangeEvent,
  Dispatch,
  SetStateAction,
  KeyboardEvent,
} from "react";
import { useQueryClient } from "@tanstack/react-query";

export const useForm = <T>(initial: T) => {
  const [state, setState] = useState<T>(initial);
  const queryClient = useQueryClient();

  const handleChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    e.preventDefault();
    const { name, value, type } = e.target;

    if (type === "file") {
      const file =
        (e.currentTarget as HTMLInputElement | null)?.files?.[0] || null;
      setState({ ...state, [name]: file });
      return;
    }
    setState({ ...state, [name]: value });
  };

  const onKeyDown = (
    e: KeyboardEvent<HTMLInputElement>,
    queryKey: string | string[]
  ) => {
    const { key } = e;

    if (key === "Enter") {
      queryClient.prefetchQuery({
        queryKey: typeof queryKey === "object" ? queryKey : [queryKey],
      });
    }
  };

  return { state, handleChange, setState, onKeyDown } as {
    state: T;
    handleChange: (
      e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
    ) => void;
    setState: Dispatch<SetStateAction<T>>;
    onKeyDown: (
      e: KeyboardEvent<HTMLInputElement>,
      queryKey: string | string[]
    ) => void;
  };
};
