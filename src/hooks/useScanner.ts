import { useState, useRef, Ref<PERSON>allback, MouseEvent } from "react";

interface PositionType {
  left: number;
  top: number;
}

export const useScanner = () => {
  const [scannerPosition, setScannerPosition] = useState<PositionType | null>(
    null
  );
  const [viewPosition, setViewPosition] = useState<PositionType | null>(null);
  const ref = useRef<DOMRect>();

  const setRect: RefCallback<Element> = (element) => {
    if (element) {
      ref.current = element.getBoundingClientRect();
    }
  };

  const onMouseMove = (e: MouseEvent<HTMLDivElement>) => {
    if (ref.current && document.documentElement.clientWidth > 768) {
      const scannerPos: PositionType = { left: 0, top: 0 };
      const scannerOffsetWidth: number =
        ref.current.width < ref.current.height
          ? ref.current.width * 0.5
          : ref.current.height * 0.5;
      const scannerOffsetHeight: number =
        ref.current.height < ref.current.width
          ? ref.current.height * 0.5
          : ref.current.width * 0.5;
      const scannerPosLeft: number =
        e.clientX - ref.current.x - scannerOffsetWidth / 2;
      const scannerPosTop: number =
        e.clientY - ref.current.y - scannerOffsetHeight / 2;

      const allowedPosLeft: boolean =
        scannerPosLeft >= 0 &&
        scannerPosLeft <= ref.current.width - scannerOffsetWidth;
      const allowedPosTop: boolean =
        scannerPosTop >= 0 &&
        scannerPosTop <= ref.current.height - scannerOffsetHeight;

      if (allowedPosLeft) {
        scannerPos.left = scannerPosLeft;
      } else if (scannerPosLeft < 0) {
        scannerPos.left = 0;
      } else {
        scannerPos.left = ref.current.width - scannerOffsetWidth;
      }

      if (allowedPosTop) {
        scannerPos.top = scannerPosTop;
      } else if (scannerPosTop < 0) {
        scannerPos.top = 0;
      } else {
        scannerPos.top = ref.current.height - scannerOffsetHeight;
      }

      setScannerPosition(scannerPos);

      setViewPosition({
        left: scannerPos.left * -3.4,
        top: scannerPos.top * -3.4,
      });
    }
  };

  const onMouseLeave = () => {
    if (ref.current && document.documentElement.clientWidth > 768) {
      setScannerPosition(null);
      setViewPosition(null);
    }
  };

  return {
    ref,
    onMouseMove,
    scannerPosition,
    setRect,
    onMouseLeave,
    viewPosition,
  };
};
