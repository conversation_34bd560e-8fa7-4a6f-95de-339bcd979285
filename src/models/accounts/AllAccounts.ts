export interface AllAccountsRequestType {
  page?: number;
  limit?: number;
  sort?: "email" | "countryNumber" | "phone" | "createdAt";
  order?: "ASC" | "DESC";
  status?: "active" | "survey_ongoing" | "survey_ready" | "deleted";
  accountId?: number;
  accountType?: "general" | "kakao" | "google" | "apple";
  generalId?: string;
  countryNumber?: string;
  phone?: string;
  email?: string;
  start?: string;
  end?: string;
}

export interface AllAccountsType {
  id: number;
  name: string;
  createdAt: string;
}

export interface AllAccountsResponseType {
  count: number;
  accounts: AllAccountsType[];
}
