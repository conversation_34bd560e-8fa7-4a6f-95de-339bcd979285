export interface GetErrorDataRequestType {
  limit: number;
  page: number;
  sort: "subjectId" | "type" | "createdAt";
  order: "ASC" | "DESC";
  start?: string;
  end?: string;
  type?: "REF" | "STRIP" | "SHADOW" | "NOSTRIP" | "UNKNOWN" | "QR_BBOX";
  analysisErrorId?: number;
  subjectId?: number;
}

export interface AnalysisErrorType {
  id: number;
  subjectId: number;
  type: "REF" | "STRIP" | "SHADOW" | "NOSTRIP" | "UNKNOWN" | "QR_BBOX";
  image?: string;
  createdAt: string;
  updatedAt: string;
}

export interface GetErrorDataResponseType {
  count: number;
  originalErrors: AnalysisErrorType[];
  cropErrors?: AnalysisErrorType[];
}
