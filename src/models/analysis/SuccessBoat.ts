export interface GetSuccessBoatRequestType {
  limit: number;
  page: number;
  sort: string;
  order: "ASC" | "DESC";
  start?: string;
  end?: string;
  boatId?: number;
  subjectId?: number;
  blood?: number;
  protein?: number;
  glucose?: number;
  ph?: number;
  ketone?: number;
  leukocytes?: number;
  bilirubin?: number;
  urobilinogen?: number;
  nitrite?: number;
  sg?: number;
}

export interface BoatItemType {
  id: number;
  subjectId: number;
  stripType: number;
  blood: number;
  ph: number;
  protein: number;
  glucose: number;
  ketone: number;
  leukocytes: number;
  bilirubin: number;
  urobilinogen: number;
  nitrite: number;
  sg: number;
  crop: string;
  mask: string;
  csv: string;
  memo: string;
  createdAt: string;
}

export interface GetSuccessBoatResponseType {
  count: number;
  boats: BoatItemType[];
}
