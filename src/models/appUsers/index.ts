export interface GetAppUserQueryRequestType {
  page?: number;
  limit: number;
  sort?: "email" | "countryNumber" | "phone" | "createdAt";
  order?: "ASC" | "DESC";
  status?: "active" | "deleted" | "survey_ready" | "survey_ongoing";
  accountId?: number;
  generalId?: string;
  accountType?: "general" | "kakao" | "google" | "apple";
  countryNumber?: number;
  nickname?: string;
  phone?: string;
  email?: string;
  start?: string;
  end?: string;
}

export interface GetAppUserSubjectType {
  id: number;
  accountId: number;
  subjectTypeId: number;
  nickname: string;
  sex: "m" | "f";
  birth: string;
  initialWeight: number;
  targetWeight: number;
  targetWater: number;
  metadata: {
    height: number;
    targetCalorie: number;
  };
  ketoneMode: boolean;
  image?: string;
  createdAt: string;
  updatedAt: string;
  status: "active" | "deleted" | "survey_ready" | "survey_ongoing" | "archived";
  subjectType: {
    id: number;
    category: string;
    type: string;
    description: string;
    status: string;
  };
}

export interface GetAppUserType {
  id: number;
  general: string;
  kakao?: string;
  google?: string;
  apple?: string;
  status: "active" | "deleted" | "survey_ready" | "survey_ongoing" | "archived";
  createdAt: string;
  updatedAt: string;
  personalInfo: {
    accountId: number;
    countryNumber: string;
    phone: string;
    email?: string | null;
    createdAt: string;
    updatedAt: string;
  };
  subjects?: GetAppUserSubjectType[];
}

export interface GetAppUserResponseType {
  count: number;
  accounts: GetAppUserType[];
}

export interface GetAppUserDetailResponseType {
  account: GetDetailUserProductionType;
}

export interface GetDetailUserProductionType extends GetAppUserType {
  // 운영서버 전용 타입
  metadata: {
    height: number;
    targetCalorie: number;
  };
}

export interface UserServeyResultType {
  question: string;
  text: string;
  choices: string[];
}

export interface UserSurveyResponseType {
  lifeStyles: UserServeyResultType[];
  foodLifes: UserServeyResultType[];
  etc: UserServeyResultType[];
}

export interface UserCareInformation {
  urine: number;
  water: number;
  weight: number;
}

export interface GetDetailCareResponseType {
  subject: {
    id: number;
    accountId: number;
    subjectTypeId: number;
    nickname: string;
    sex: "m" | "f";
    initialWeight: number;
    targetWeight: number;
    targetWater: number;
    metadata: {
      height: number;
      targetCalorie: number;
    };
    ketoneMode: boolean;
    image?: string;
    createdAt: string;
    updatedAt: string;
  };
  average: number;
  dailyData: { date: string; value: number }[];
  records: {
    date: string;
    values: {
      id: number;
      value: number;
      createdAt: string;
    }[];
  }[];
  recentWeight: {
    createdAt: string;
    id: number;
    value: string;
  };
  monthlyData?: {
    date: string;
    value: number;
  }[];
}

export interface FoodType {
  id: number;
  korean: string;
  english: string;
  amount: number;
  calorie: number;
  carb: number;
  protein: number;
  fat: number;
  sugar: number;
  sodium: number;
  ingredient: string;
}

export interface BookmarkType {
  id: number;
  status: string;
  createdAt: string;
  food: FoodType;
}

export interface GetBookmarkResponseType {
  count: number;
  bookmarks: BookmarkType[];
}

export interface FoodRecordsType {
  breakfastCalories: number;
  breakfasts: {
    id: number;
    type: "breakfast" | "lunch" | "dinner" | "snack";
    gram: number;
    createdAt: string;
    food: FoodType;
  }[];
  lunchCalories: number;
  lunches: {
    id: number;
    type: "breakfast" | "lunch" | "dinner" | "snack";
    gram: number;
    createdAt: string;
    food: FoodType;
  }[];
  dinnerCalories: number;
  dinners: {
    id: number;
    type: "breakfast" | "lunch" | "dinner" | "snack";
    gram: number;
    createdAt: string;
    food: FoodType;
  }[];
  snackCalories: number;
  snacks: {
    id: number;
    type: "breakfast" | "lunch" | "dinner" | "snack";
    gram: number;
    createdAt: string;
    food: FoodType;
  }[];
}
