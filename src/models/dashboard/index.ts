export interface AccountCountsType {
  total: number;
  activeStatusAccounts: number;
  surveyReadyStatusAccounts: number;
  surveyOngoingStatusAccounts: number;
  archivedStatusAccounts: number;
  inactiveStatusAccounts: number;
  deletedStatusAccounts: number;
}

export interface AccountTypeCountsType {
  general: number;
  kakao: number;
  google: number;
  apple: number;
}

export interface WeeklyAccountCountsType {
  date: string;
  count: number;
  general: number;
  kakao: number;
  google: number;
  apple: number;
}

export interface WeeklyTestCountsType {
  date: string;
  count: number;
  success: number;
  error: number;
}

export interface SubjectCountsType {
  total: number;
  activeStatusSubjects: number;
  deletedStatusSubjects: number;
}

export interface BoatType {
  total: number;
  UNKNOWN?: number;
  QR_BBOX?: number;
  REF?: number;
  QR?: number;
  SHADOW?: number;
  STRIP?: number;
  NOSTRIP?: number;
}

export interface TestCountsType {
  total: number;
  success: {
    boat: BoatType;
  };
  error: {
    boat: BoatType;
  };
}

export interface DashBoardResponseType {
  accountCounts: AccountCountsType;
  accountTypeCounts: AccountTypeCountsType;
  weeklyAccountCounts: WeeklyAccountCountsType[];
  weeklyTestCounts: WeeklyTestCountsType[];
  subjectCounts: SubjectCountsType;
  testCounts: TestCountsType;
}
