export interface PhoneNotificationRequestType {
  page: number;
  limit: number;
  sort: "createdAt" | "phone" | "name";
  order: "ASC" | "DESC";
}

export interface PhoneNotificationType {
  id: number;
  name: string;
  phone: number;
  status: string;
  createdAt: string;
  updatedAt: string;
}

export interface PhoneNotificationResponseType {
  count: number;
  notifications: PhoneNotificationType[];
}
