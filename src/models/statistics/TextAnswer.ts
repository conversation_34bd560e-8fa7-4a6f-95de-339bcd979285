export interface TextAnswserRequestType {
  limit: number;
  page: number;
  sort: "createdAt" | "subjectId" | "text";
  order: "ASC" | "DESC";
  questionId?: 1 | 3 | 9;
}

export interface TextAnswerType {
  id: number;
  questionId: number;
  subjectId: number;
  text: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

export interface TextAnswerResponseType {
  count: number;
  textAnswers: TextAnswerType[];
}
