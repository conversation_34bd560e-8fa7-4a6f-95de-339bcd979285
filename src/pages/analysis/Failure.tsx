import {
  Con<PERSON>er,
  PageTitle,
  Switch,
  Input,
  Select,
  Button,
  ImageModal,
  AnalysisTableRow,
  Wrapper,
} from "@/components";
import { Clue } from "@/assets";
import { useHover, useSwitch, useModal, useForm } from "@/hooks";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
} from "@mui/material";
import type { AnalysisErrorType, GetErrorDataRequestType } from "@/models";
import { useQueryClient } from "@tanstack/react-query";
import { useGetErrorBoatQuery } from "@/apis/analysis";
import { ChangeEvent, useEffect } from "react";
import { BoatOriginalOptions, BoatCropOptions } from "@/constants";
import { useSearchParams } from "react-router-dom";

export const AnalysisFailurePage = () => {
  const queryClient = useQueryClient();
  const params = new URLSearchParams(location.search);
  const start = params.get("start") ?? undefined;
  const end = params.get("end") ?? undefined;
  const [searchParams] = useSearchParams();

  const subjectIdParams: string | null = searchParams.get("subjectId");

  const {
    state: searchOptions,
    handleChange: handleSearchOptionsChange,
    setState: setSearchOptionsState,
    onKeyDown,
  } = useForm<GetErrorDataRequestType>({
    limit: 15,
    page: 1,
    sort: "createdAt",
    order: "DESC",
    start,
    end,
    ...(subjectIdParams !== null && { subjectId: Number(subjectIdParams) }),
  });
  const { modalState } = useModal("crop");
  const { state, handleChange } = useSwitch();
  const { onHover, onHoverOut, isHover } = useHover({
    clue: false,
  });
  const { data, refetch } = useGetErrorBoatQuery(searchOptions, state);

  useEffect(() => {
    refetch();
  }, [
    searchOptions.page,
    refetch,
    searchOptions.type,
    state,
    searchOptions.limit,
  ]);

  return (
    <Wrapper>
      {modalState.isOpen && <ImageModal title="crop image" modalKey="crop" />}
      <Container>
        <PageTitle title="Analysis Result" num={data?.count as number}>
          <Switch
            style={{ marginLeft: "10px" }}
            onChange={handleChange}
            value={state}
          />
          <span>{state === "on" ? "crop" : "Original"}</span>
        </PageTitle>
      </Container>
      <Container>
        <Container width="fit-contents">
          <Input
            placeholder="ID"
            width={90}
            name="analysisErrorId"
            value={searchOptions.analysisErrorId || ""}
            onChange={handleSearchOptionsChange}
            onKeyDown={(e) => {
              onKeyDown(e, ["errorBoat"]);
            }}
          />
          <Input
            placeholder="Subject ID"
            width={110}
            name="subjectId"
            value={searchOptions.subjectId || ""}
            onChange={handleSearchOptionsChange}
            onKeyDown={(e) => {
              onKeyDown(e, ["errorBoat"]);
            }}
          />
          <Select
            options={state === "off" ? BoatOriginalOptions : BoatCropOptions}
            width={130}
            name="type"
            value={searchOptions.type || ""}
            onChange={handleSearchOptionsChange}
            showArrow
          />
          <Input
            placeholder="Start"
            type="date"
            width={130}
            name="start"
            value={searchOptions.start}
            onChange={handleSearchOptionsChange}
          />
          <Input
            placeholder="End"
            type="date"
            width={130}
            name="end"
            value={searchOptions.end}
            onChange={handleSearchOptionsChange}
          />
          <Button
            onMouseEnter={() => onHover("clue")}
            onMouseLeave={() => onHoverOut("clue")}
            onClick={() =>
              queryClient.prefetchQuery({ queryKey: ["errorBoat"] })
            }
            padding="0.3rem"
            border={false}
          >
            <Clue
              color={isHover["clue"] ? "#fff" : "#41d8e6"}
              width="1rem"
              height="1rem"
            />
          </Button>
        </Container>
        <Input placeholder="Number of line" width={138} />
      </Container>
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>ID</TableCell>
              <TableCell>Subject ID</TableCell>
              <TableCell>Image</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Created</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {state === "on" ? (
              <AnalysisTableRow
                data={data?.cropErrors as AnalysisErrorType[]}
              />
            ) : (
              <AnalysisTableRow
                data={data?.originalErrors as AnalysisErrorType[]}
              />
            )}
          </TableBody>
        </Table>
        <TablePagination
          component="div"
          count={data?.count ?? 0}
          page={(searchOptions.page as number) - 1}
          rowsPerPageOptions={[15, 20, 25, 30]}
          rowsPerPage={searchOptions.limit}
          onRowsPerPageChange={(e: ChangeEvent<HTMLInputElement>) => {
            const { value } = e.target;

            setSearchOptionsState((prev) => ({
              ...prev,
              limit: Number(value),
            }));
          }}
          onPageChange={(_, num) =>
            setSearchOptionsState((prev) => ({ ...prev, page: num + 1 }))
          }
        />
      </TableContainer>
    </Wrapper>
  );
};
