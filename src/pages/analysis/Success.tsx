import {
  useGetAnalysisBoatQuery,
  useSelectedBoatDownloadMutation,
  useAllBoatsCsv,
} from "@/apis/analysis";
import { Clue, DownArrow, File, Pencil, CsvSvg } from "@/assets";
import {
  Button,
  Container,
  Input,
  PageTitle,
  Switch,
  Image,
  AnalysisSuccessModal,
  ImageModal,
  Wrapper,
} from "@/components";
import {
  useHover,
  useModal,
  useSwitch,
  useForm,
  useCsv,
  useImage,
} from "@/hooks";
import { GetSuccessBoatRequestType } from "@/models/analysis";
import {
  Checkbox,
  Table,
  TableRow,
  TableHead,
  TableBody,
  TableContainer,
  TableCell,
  TablePagination,
} from "@mui/material";
import { ChangeEvent, useEffect, useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { getDateFormat } from "@/utils";

export const AnalysisSuccessPage = () => {
  const queryClient = useQueryClient();
  const [downloadCsv] = useCsv();
  const { state: value, handleChange } = useSwitch();
  const [selected, setSelected] = useState<number[]>([]);
  const [memo, setMemo] = useState<string>("");
  const [memoIdx, setMemoIdx] = useState<number>(0);
  const {
    state: searchState,
    handleChange: handleSearchStateChange,
    setState: setSearchState,
  } = useForm<GetSuccessBoatRequestType>({
    page: 1,
    sort: "createdAt",
    order: "DESC",
    limit: 15,
    subjectId: undefined,
  });
  const { mutate: selectedDownloadCsv } =
    useSelectedBoatDownloadMutation(selected);
  const [, handleImageChange] = useImage();
  const { modalState, openModal } = useModal("analysisSuccess");
  const { modalState: cropModalState, openModal: openCropModal } =
    useModal("crop");
  const { modalState: maskModalState, openModal: openMaskModal } =
    useModal("mask");
  const { isHover, onHover, onHoverOut } = useHover({
    clue: false,
    export: false,
    arrow: false,
  });
  const { data, refetch } = useGetAnalysisBoatQuery(searchState);
  const queryData = useAllBoatsCsv();

  const handleAllBoatDownloadClick = async () => {
    await queryClient.prefetchQuery({ queryKey: ["allBoatCsv"] });
    const data = queryClient.getQueryData(["allBoatCsv"]);

    console.log(queryData);

    if (data === undefined) {
      alert("데이터가 비어있습니다");
      return;
    }

    downloadCsv(data as string, "all-boats-data", "csv");
  };

  const handleSelectedDownloadClick = () => {
    if (selected.length === 0) {
      alert("한개 이상 체크해주세요.");
      return;
    }

    selectedDownloadCsv();
  };

  useEffect(() => {
    refetch();
  }, [searchState.page, refetch, searchState.limit]);

  return (
    <Wrapper>
      {modalState.isOpen && (
        <AnalysisSuccessModal memoInitial={memo} memoIdx={memoIdx} />
      )}
      {cropModalState.isOpen && (
        <ImageModal modalKey="crop" title="Crop Image" />
      )}
      {maskModalState.isOpen && (
        <ImageModal modalKey="mask" title="Mask Image" />
      )}
      <Container>
        <PageTitle title="Analysis Result" num={data?.count as number} />
        <Switch onChange={handleChange} value={value} />
      </Container>
      <Container>
        <Container width="fit-contents">
          <Input
            placeholder="ID"
            width={90}
            name="boatId"
            value={searchState.boatId}
            onChange={handleSearchStateChange}
          />
          <Input
            placeholder="Subject ID"
            width={110}
            name="subjectId"
            value={searchState.subjectId}
            onChange={handleSearchStateChange}
          />
          <Input
            placeholder="Start"
            type="date"
            width={130}
            name="start"
            value={searchState.start}
            onChange={handleSearchStateChange}
          />
          <Input
            placeholder="End"
            type="date"
            width={130}
            name="end"
            value={searchState.end}
            onChange={handleSearchStateChange}
          />
          <Button
            onMouseEnter={() => onHover("clue")}
            onMouseLeave={() => onHoverOut("clue")}
            padding="0.3rem"
            border={false}
            onClick={() =>
              queryClient.prefetchQuery({ queryKey: ["successAnalysis"] })
            }
          >
            <Clue
              color={isHover["clue"] ? "#fff" : "#41d8e6"}
              width="1rem"
              height="1rem"
            />
          </Button>
        </Container>
        <Container width="fit-contents">
          <Input placeholder="Number of line" width={138} />
          <Button
            onMouseEnter={() => onHover("export")}
            onMouseLeave={() => onHoverOut("export")}
            onClick={handleAllBoatDownloadClick}
            style={{ marginRight: "0.5rem" }}
          >
            <File
              color={isHover["export"] ? "#fff" : "#41d8e6"}
              width="1rem"
              height="1rem"
            />
            All Export
          </Button>
          <Button
            onMouseEnter={() => onHover("arrow")}
            onMouseLeave={() => onHoverOut("arrow")}
            onClick={handleSelectedDownloadClick}
          >
            <DownArrow
              color={isHover["arrow"] ? "#FFFFFF" : "#41d8e6"}
              width="1rem"
              height="1rem"
            />
            Export
          </Button>
        </Container>
      </Container>
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>
                <Checkbox
                  checked={selected.length === 15}
                  onClick={() => {
                    if (selected.length === 15) {
                      setSelected([]);
                      return;
                    }

                    setSelected(data?.boats.map((item) => item.id) as number[]);
                  }}
                />
              </TableCell>
              <TableCell>ID</TableCell>
              <TableCell>Subject ID</TableCell>
              <TableCell>Strip Type</TableCell>
              <TableCell>Blood</TableCell>
              <TableCell>PH</TableCell>
              <TableCell>Protein</TableCell>
              <TableCell>Glucose</TableCell>
              <TableCell>Ketone</TableCell>
              <TableCell>Leukocytes</TableCell>
              <TableCell>Bilirubin</TableCell>
              <TableCell>Urobilinogen</TableCell>
              <TableCell>Nitrite</TableCell>
              <TableCell>S.G</TableCell>
              <TableCell>Crop</TableCell>
              <TableCell>Mask</TableCell>
              <TableCell>Memo</TableCell>
              <TableCell>Created</TableCell>
              <TableCell>Action</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {data?.boats.map((item, idx) => (
              <TableRow
                key={idx}
                sx={{
                  ":hover": {
                    background: "#ededed",
                  },
                  "&:last-child td, &:last-child th": {
                    border: 0,
                  },
                }}
              >
                <TableCell>
                  <Checkbox
                    onClick={() => {
                      if (selected.includes(item.id)) {
                        setSelected((prev) =>
                          prev.filter((id) => id !== item.id)
                        );
                        return;
                      }

                      setSelected((prev) => [...prev, item.id]);
                    }}
                    checked={selected.includes(item.id)}
                  />
                </TableCell>
                <TableCell>{item.id}</TableCell>
                <TableCell>{item.subjectId}</TableCell>
                <TableCell>{item.stripType}</TableCell>
                <TableCell>{item.blood}</TableCell>
                <TableCell>{item.ph}</TableCell>
                <TableCell>{item.protein}</TableCell>
                <TableCell>{item.glucose}</TableCell>
                <TableCell>{item.ketone}</TableCell>
                <TableCell>{item.leukocytes ?? "-"}</TableCell>
                <TableCell>{item.bilirubin ?? "-"}</TableCell>
                <TableCell>{item.urobilinogen ?? "-"}</TableCell>
                <TableCell>{item.nitrite ?? "-"}</TableCell>
                <TableCell>{item.sg ?? "-"}</TableCell>
                <TableCell>
                  <Image
                    src={item.crop}
                    alt={`${item.id}`}
                    onClick={() => {
                      handleImageChange(item.crop);
                      openCropModal();
                    }}
                    width="20px"
                    loading="lazy"
                    height="20px"
                  />
                </TableCell>
                <TableCell>
                  <Image
                    src={item.mask}
                    alt={`${item.id}`}
                    onClick={() => {
                      handleImageChange(item.mask);
                      openMaskModal();
                    }}
                    width="20px"
                    loading="lazy"
                    height="20px"
                  />
                </TableCell>
                <TableCell>{item.memo}</TableCell>
                <TableCell>{getDateFormat(item.createdAt)}</TableCell>
                <TableCell>
                  <CsvSvg
                    color="#04a954"
                    width="1rem"
                    height="1rem"
                    style={{ marginRight: "10px" }}
                  />
                  <Pencil
                    onClick={() => {
                      setMemo(item.memo);
                      setMemoIdx(item.id);
                      openModal();
                    }}
                    width="1rem"
                    height="1rem"
                    color="#04a954"
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <TablePagination
          component="div"
          count={data?.count ?? 0}
          page={(searchState.page as number) - 1}
          rowsPerPageOptions={[15, 20, 25, 30]}
          rowsPerPage={searchState.limit}
          onRowsPerPageChange={(e: ChangeEvent<HTMLInputElement>) => {
            const { value } = e.target;

            setSearchState((prev) => ({ ...prev, limit: Number(value) }));
          }}
          onPageChange={(_, num) =>
            setSearchState((prev) => ({ ...prev, page: num + 1 }))
          }
        />
      </TableContainer>
    </Wrapper>
    // </>
  );
};
