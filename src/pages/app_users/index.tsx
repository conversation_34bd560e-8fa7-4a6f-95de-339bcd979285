import {
  Select,
  Container,
  But<PERSON>,
  Input,
  PageTitle,
  AppUserModal,
  DetailProfileModal,
  Wrapper,
} from "@/components";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
} from "@mui/material";
import styled from "@emotion/styled";
import { capitalize, getDateFormat } from "@/utils";
import { File, Pencil, Clue } from "@/assets";
import { useHover, useModal, useForm, useCsv } from "@/hooks";
import { useGetAppUserQuery, getAppUserCsv } from "@/apis/appUsers";
import { useQueryClient } from "@tanstack/react-query";
import { GetAppUserQueryRequestType } from "@/models";
import { accountAtom, statusAtom } from "@/atoms";
import { useSetRecoilState } from "recoil";
import { ChangeEvent, useEffect } from "react";

const AppUsersPage = () => {
  const {
    state: searchState,
    handleChange,
    setState,
    onKeyDown,
  } = useForm<GetAppUserQueryRequestType>({
    page: 1,
    sort: "createdAt",
    order: "DESC",
    limit: 15,
    accountType: undefined,
    status: undefined,
    email: "",
    phone: "",
  });
  const queryClient = useQueryClient();
  const [downloadCsv] = useCsv();
  const { data, refetch } = useGetAppUserQuery(searchState);
  const { isHover, onHoverOut, onHover } = useHover({
    clue: false,
    file: false,
  });
  const setStatus = useSetRecoilState(statusAtom);
  const setAccountId = useSetRecoilState<number>(accountAtom);
  const { modalState, openModal } = useModal("appUser");
  const {
    modalState: detailProfileModalState,
    openModal: openDetailProfileModal,
  } = useModal("detailProfile");

  const handleClickSearch = () => {
    if (
      searchState.status ===
      ("archived" as
        | "archived"
        | "active"
        | "deleted"
        | "survey_ready"
        | "survey_ongoing")
    ) {
      alert("archived 상태는 검색할 수 없습니다.");
      return;
    }

    queryClient.prefetchQuery({ queryKey: ["getAppUsers"] });
  };

  const handleDownloadCsvClick = () => {
    getAppUserCsv()
      .then(({ data }) => {
        downloadCsv(data, "appUsers", "csv");
      })
      .catch((err) => {
        console.error(err);

        alert("csv를 불러오는데 실패하였습니다. 다시 시도해주세요.");
      });
  };

  useEffect(() => {
    refetch();
  }, [searchState.page, refetch, searchState.limit]);

  return (
    <Wrapper>
      {modalState.isOpen && <AppUserModal />}
      {detailProfileModalState.isOpen && <DetailProfileModal />}
      <PageTitle title="App Users" num={data?.count as number} />
      <Container>
        <Container width="fit-contents">
          <Input
            placeholder="ID"
            name="accountId"
            width={50}
            onChange={handleChange}
            value={searchState.generalId}
            onKeyDown={(e) => onKeyDown(e, "getAppUsers")}
          />
          <Select
            name="accountType"
            onChange={handleChange}
            value={searchState.accountType}
            options={[
              {
                label: "All Account Type",
                value: "",
              },
              {
                label: "General",
                value: "general",
              },
              {
                label: "Kakao",
                value: "kakao",
              },
              {
                label: "Google",
                value: "google",
              },
              {
                label: "Apple",
                value: "apple",
              },
            ]}
            showArrow
          />
          <Select
            options={[
              {
                label: "All Status",
                value: "",
              },
              {
                label: "Active",
                value: "active",
              },
              {
                label: "Archieved",
                value: "archived",
              },
              {
                label: "Deleted",
                value: "deleted",
              },
              {
                label: "Survey Ready",
                value: "survey_ready",
              },
              {
                label: "Survey Ongoing",
                value: "survey_ongoing",
              },
            ]}
            name="status"
            onChange={handleChange}
            value={searchState.status}
            showArrow
          />
          <Input
            placeholder="Email"
            width={120}
            name="email"
            value={searchState.email}
            onChange={handleChange}
            onKeyDown={(e) => onKeyDown(e, "getAppUsers")}
          />
          <Input
            placeholder="Phone"
            width={120}
            name="phone"
            value={searchState.phone}
            onChange={handleChange}
            onKeyDown={(e) => onKeyDown(e, "getAppUsers")}
          />
          <Input
            placeholder="Join Date"
            type="date"
            width={130}
            value={searchState?.end}
            onChange={(e) => {
              setState((prev) => ({
                ...prev,
                start: e.target.value,
                end: e.target.value,
              }));
            }}
          />
          <Button
            border={false}
            onMouseEnter={() => onHover("clue")}
            onMouseLeave={() => onHoverOut("clue")}
            padding="0.3rem"
            onClick={handleClickSearch}
          >
            <Clue
              color={isHover["clue"] ? "#fff" : "#41d8e6"}
              width="1rem"
              height="1rem"
            />
          </Button>
        </Container>
        <Button
          onMouseEnter={() => onHover("file")}
          onMouseLeave={() => onHoverOut("file")}
          onClick={handleDownloadCsvClick}
        >
          <File
            color={isHover["file"] ? "#fff" : "#41d8e6"}
            width="1rem"
            height="1rem"
          />
          Down
        </Button>
      </Container>
      <TableContainer style={{ fontFamily: "Gilroy" }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>ID</TableCell>
              <TableCell>Registration</TableCell>
              <TableCell>Email</TableCell>
              <TableCell>Conuntry Code</TableCell>
              <TableCell>Phone</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Join Date</TableCell>
              <TableCell></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {data?.accounts.map((item, idx) => (
              <TableRow
                key={idx}
                sx={{
                  ":hover": { background: "#ededed" },
                  "&:last-child td, &:last-child th": { border: 0 },
                }}
                onClick={() => {
                  openDetailProfileModal();
                  setAccountId(item.id);
                }}
              >
                <_TableCell>{item.id}</_TableCell>
                <_TableCell>
                  {item.kakao
                    ? "Kakao"
                    : item.google
                    ? "Google"
                    : item.apple
                    ? "Apple "
                    : item.general}
                </_TableCell>
                <_TableCell>
                  {item.personalInfo?.email !== null
                    ? item.personalInfo?.email
                    : ""}
                </_TableCell>
                <_TableCell>{item.personalInfo?.countryNumber}</_TableCell>
                <_TableCell>{item.personalInfo?.phone}</_TableCell>
                <_TableCell>
                  {capitalize(item.status.split("_").join(" "))}
                </_TableCell>
                <_TableCell>{getDateFormat(item.createdAt)}</_TableCell>
                <_TableCell>
                  {(item.status === "active" || item.status === "archived") && (
                    <Pencil
                      onClick={(e) => {
                        e.stopPropagation();
                        setStatus({
                          accountId: item.personalInfo.accountId,
                          status: capitalize(item.status) as
                            | "Active"
                            | "Archived",
                        });
                        openModal();
                      }}
                      width="1rem"
                      height="1rem"
                      color="#04a954"
                    />
                  )}
                </_TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        component="div"
        count={data?.count ?? 0}
        page={(searchState.page as number) - 1}
        rowsPerPageOptions={[15, 20, 25, 30]}
        rowsPerPage={searchState.limit}
        onRowsPerPageChange={(e: ChangeEvent<HTMLInputElement>) => {
          const { value } = e.target;

          setState((prev) => ({ ...prev, limit: Number(value) }));
        }}
        onPageChange={(_, num) =>
          setState((prev) => ({ ...prev, page: num + 1 }))
        }
      />
    </Wrapper>
  );
};

export default AppUsersPage;

const _TableCell = styled(TableCell)`
  border: 0;
`;
