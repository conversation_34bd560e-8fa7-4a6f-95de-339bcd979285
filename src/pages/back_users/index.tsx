import { useDeleteAdminMutation, useGetAllAccountsQuery } from "@/apis/admin";
import { Clue } from "@/assets";
import {
  Button,
  Input,
  PageTitle,
  UserEditModal,
  UserInformation,
  Wrapper,
} from "@/components";
import { useHover, useModal, useForm, useInput } from "@/hooks";
import { AllAccountsRequestType } from "@/models";
import { getDateFormat } from "@/utils";
import styled from "@emotion/styled";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
} from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import { ChangeEvent, useEffect } from "react";

export const BackUsersPage = () => {
  const queryClient = useQueryClient();
  const [accountId, , setAccountIdState] = useInput<number, HTMLInputElement>(
    0
  );
  const { modalState } = useModal("userEdit");
  const { isHover, onHover, onHoverOut } = useHover({
    clue: false,
  });
  const {
    state,
    setState,
    handleChange: handleIdChange,
    onKeyDown,
  } = useForm<AllAccountsRequestType>({
    page: 1,
    limit: 15,
    sort: "createdAt",
    order: "DESC",
  });
  const { data, refetch } = useGetAllAccountsQuery(state);
  const { mutate: deleteUser } = useDeleteAdminMutation();

  useEffect(() => {
    refetch();
  }, [refetch, state.page, state.limit]);

  return (
    <Wrapper>
      <_Wrapper>
        {modalState.isOpen && <UserEditModal />}
        <_Container>
          <PageTitle title="Users" num={data?.count as number} />
          <_MenuBar>
            <_MenuSide>
              <Input
                placeholder="ID"
                width={90}
                name="accountId"
                value={state.accountId}
                onChange={handleIdChange}
                onKeyDown={(e) => onKeyDown(e, ["allAccounts"])}
              />
              <Button
                padding="0.3rem"
                border={false}
                onMouseEnter={() => onHover("clue")}
                onMouseLeave={() => onHoverOut("clue")}
                onClick={() =>
                  queryClient.prefetchQuery({ queryKey: ["allAccounts"] })
                }
              >
                <Clue
                  color={isHover["clue"] ? "#FFFFFF" : "#41d8e6"}
                  width="1rem"
                  height="1rem"
                />
              </Button>
            </_MenuSide>
            <Button
              onClick={() => {
                const confirm = window.confirm("삭제하시겠습니까?");

                if (confirm) deleteUser(accountId);
                else alert("취소되었습니다.");
              }}
            >
              Delete
            </Button>
          </_MenuBar>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>User ID</TableCell>
                  <TableCell>Name</TableCell>
                  <TableCell>Created</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {data?.accounts.map((item) => (
                  <TableRow
                    key={item.id}
                    onClick={() => setAccountIdState(item.id)}
                    sx={{
                      cursor: "pointer",
                      ":hover": { background: "#ededed" },
                      "&:last-child td, &:last-child th": { border: 0 },
                      ":focus": {
                        background: "#fffa90",
                      },
                    }}
                    tabIndex={-1}
                  >
                    <TableCell sx={{ border: 0 }}>{item.id}</TableCell>
                    <TableCell sx={{ border: 0 }}>{item.name}</TableCell>
                    <TableCell sx={{ border: 0 }}>
                      {getDateFormat(item.createdAt)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            <TablePagination
              component="div"
              count={data?.count ?? 0}
              page={(state.page as number) - 1}
              rowsPerPageOptions={[15, 20, 25, 30]}
              rowsPerPage={state.limit || 1}
              onRowsPerPageChange={(e: ChangeEvent<HTMLInputElement>) => {
                const { value } = e.target;

                setState((prev) => ({ ...prev, limit: Number(value) }));
              }}
              onPageChange={(_, num) =>
                setState((prev) => ({ ...prev, page: num + 1 }))
              }
            />
          </TableContainer>
        </_Container>
        <_UserContainer>
          {accountId === 0 ? (
            <h2>No user Selected.</h2>
          ) : (
            <UserInformation accountId={accountId} />
          )}
        </_UserContainer>
      </_Wrapper>
    </Wrapper>
  );
};

const _Wrapper = styled.div`
  width: 100%;
  // min-height: 100vh;
  display: flex;
`;

const _Container = styled.div`
  width: 50%;
  display: flex;
  flex-direction: column;
`;

const _MenuBar = styled.div`
  width: 100%;
  display: flex;
  justify-content: space-between;
`;

const _MenuSide = styled.div`
  display: flex;
`;

const _UserContainer = styled(_Container)`
  height: 100%;
  padding: 4.7rem 1rem;
`;
