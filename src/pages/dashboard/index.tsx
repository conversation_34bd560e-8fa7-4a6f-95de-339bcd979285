import styled from "@emotion/styled";

import Funnel from "@/components/dashboard/Funnel";
import AccountsState from "@/components/dashboard/AccountsState";
import Pie<PERSON>hart from "@/components/dashboard/PieChart";
import DonutChart from "@/components/dashboard/DonutChart";
import LineChart from "@/components/dashboard/LineChart";

import { useGetDashBoardDataQuery } from "@/apis/dashboard";
import { calculatePercentage } from "@/utils";

const Dashboard = () => {
  const { data: dashboardData } = useGetDashBoardDataQuery();

  return (
    <>
      <_DashboardWrapper>
        <_LeftContainer>
          {dashboardData && (
            <>
              <_Card>
                <_TitleWrapper>
                  <_Subtitle>전체 계정 수</_Subtitle>
                  <_Value>{dashboardData?.accountCounts?.total}</_Value>
                </_TitleWrapper>
                <AccountsState accountsObj={dashboardData?.accountCounts} />
              </_Card>
              <_Card>
                <_TitleWrapper>
                  <_Subtitle>가입 경로</_Subtitle>
                </_TitleWrapper>
                <Funnel funnelObj={dashboardData?.accountTypeCounts} />
              </_Card>
            </>
          )}
        </_LeftContainer>

        <_CenterContainer>
          <_Card>
            <_TitleWrapper>
              <_Subtitle>주간 가입자 현황</_Subtitle>
              <_Value>
                Today: {dashboardData?.weeklyAccountCounts?.[6]?.count}
              </_Value>
            </_TitleWrapper>
            <LineChart graphData={dashboardData?.weeklyAccountCounts || []} />
          </_Card>

          <_Card>
            <_TitleWrapper>
              <_Subtitle>주간 검사 현황</_Subtitle>
            </_TitleWrapper>
            <LineChart graphData={dashboardData?.weeklyTestCounts || []} />
          </_Card>
        </_CenterContainer>

        <_RightContainer>
          <_Card>
            <_TitleWrapper>
              <_Subtitle>
                전체 검사결과 (성공률:
                {calculatePercentage(
                  dashboardData?.testCounts.success.boat.total || 0,
                  dashboardData?.testCounts.total || 0
                )}
                %)
              </_Subtitle>
              <_Value>{dashboardData?.testCounts?.total}</_Value>
            </_TitleWrapper>
            <ChartWrapper>
              <DonutChart
                chartData={
                  dashboardData?.testCounts || {
                    total: 0,
                    success: {
                      boat: {
                        total: 0,
                      },
                    },
                    error: {
                      boat: {
                        total: 0,
                        UNKNOWN: 0,
                        QR_BBOX: 0,
                        REF: 0,
                        QR: 0,
                        SHADOW: 0,
                        STRIP: 0,
                        NOSTRIP: 0,
                      },
                    },
                  }
                }
              />
            </ChartWrapper>
          </_Card>
          <_Card>
            <_TitleWrapper>
              <_Subtitle>검사 실패</_Subtitle>
              <_Value>{dashboardData?.testCounts?.error?.boat?.total}</_Value>
            </_TitleWrapper>
            <ChartWrapper>
              <PieChart
                chartData={
                  dashboardData?.testCounts?.error?.boat || {
                    total: 0,
                    UNKNOWN: 0,
                    QR_BBOX: 0,
                    REF: 0,
                    QR: 0,
                    SHADOW: 0,
                    STRIP: 0,
                    NOSTRIP: 0,
                  }
                }
              />
            </ChartWrapper>
          </_Card>
        </_RightContainer>
      </_DashboardWrapper>
    </>
  );
};

const _DashboardWrapper = styled.div`
  // width: 100%;
  // padding: 20px;
  // background-color: var(--color-bg-dark);
  // border-radius: 10px 0 0 0;
  display: flex;
  justify-content: center;
  gap: 15px;
  /* flex-direction: column; */
`;

const _LeftContainer = styled.div`
  width: calc(20% - 16.5px);
  display: flex;
  flex-direction: column;
  gap: 15px;
`;
const _RightContainer = styled.div`
  width: calc(30% - 16.5px);
  display: flex;
  flex-direction: column;
  gap: 15px;
`;

const _CenterContainer = styled.div`
  width: 50%;
  display: flex;
  flex-direction: column;
  gap: 15px;
`;

const _Card = styled.div`
  box-shadow: 1px 2px 6px rgba(0, 0, 0, 0.1);
  /* height: 40%; */
  border-radius: 10px;
  padding: 25px 28px;
  background-color: var(--color-card-bg-dark);
`;

const _TitleWrapper = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20px;
`;

const _Subtitle = styled.div`
  color: var(--color-text-dark);
  font-size: 18px;
  font-weight: 500;
`;

const _Value = styled.div`
  font-size: 1.2rem;
  color: var(--color-grey-text-dark);
  font-family: "GilroyMedium";
`;

const ChartWrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
`;

export default Dashboard;
