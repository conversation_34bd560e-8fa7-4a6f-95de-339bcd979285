import styled from "@emotion/styled";
import * as Tabs from "@radix-ui/react-tabs";
import { BOLogo } from "@/assets/svgs/Icons";
import SignInForm from "@/components/login/SignInForm";
import SignUpForm from "@/components/login/SignUpForm";

const LoginPage = () => {
  return (
    <LoginPageContainer>
      <CardWrapper>
        <LogoWrapper>
          <BOLogo className="" />
        </LogoWrapper>
        <TabsWrapper>
          <Tabs.Root className="TabsRoot" defaultValue="LoginTab">
            <Tabs.List className="TabsList">
              <Tabs.Trigger className="TabsTrigger" value="LoginTab">
                <span>SIGNIN</span>
              </Tabs.Trigger>
              <Tabs.Trigger className="TabsTrigger" value="SignupTab">
                <span>SIGNUP</span>
              </Tabs.Trigger>
            </Tabs.List>
            <Tabs.Content className="TabsContent" value="LoginTab">
              <SignInForm />
            </Tabs.Content>

            <Tabs.Content className="TabsContent" value="SignupTab">
              <SignUpForm />
            </Tabs.Content>
          </Tabs.Root>
        </TabsWrapper>
      </CardWrapper>
    </LoginPageContainer>
  );
};

const LoginPageContainer = styled.div`
  background-color: var(--color-bg-dark);
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
`;

const CardWrapper = styled.div`
  // box-shadow: 1px 2px 6px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 2px 4px 0px rgba(59, 63, 78, 0.03),
    0px 4px 10px 0px rgba(79, 68, 195, 0.05);
  min-width: 450px;
  width: 20%;
  gap: 30px;
  border-radius: 15px;
  padding: 40px 25px;
  background-color: var(--color-card-bg-dark);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
`;

const LogoWrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: start;
  color: var(--color-text-dark);
  svg {
    width: 80%;
  }
`;

const TabsWrapper = styled.div`
  letter-spacing: 0.03em;
  display: flex;
  justify-content: center;
  font-family: "Gilroy";

  button,
  fieldset,
  input {
    all: unset;
  }

  .TabsRoot {
    display: flex;
    flex-direction: column;
    width: 400px;
  }

  .TabsList {
    flex-shrink: 0;
    display: flex;
  }

  .TabsTrigger {
    cursor: pointer;
    font-family: inherit;
    background-color: transparent;
    padding: 0 20px;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    line-height: 1;
    color: var(--color-grey-text-dark);
    user-select: none;
  }
  .TabsTrigger:first-of-type {
    border-top-left-radius: 6px;
  }
  .TabsTrigger:last-child {
    border-top-right-radius: 6px;
  }
  .TabsTrigger:hover {
    color: #41d8e6;
  }
  .TabsTrigger[data-state="active"] {
    color: #41d8e6;
    font-family: "GilroyMedium";
    span {
      color: var(--color-text-dark);
      box-shadow: inset 0 -10px 0 rgba(5, 216, 230, 0.4);
      padding: 0 10px;
    }
  }
  .TabsTrigger:focus {
    position: relative;
  }

  .TabsContent {
    flex-grow: 1;
    padding: 40px 20px 90px;
    background-color: transparent;
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
    outline: none;
    position: relative;
  }

  .Fieldset {
    margin-bottom: 15px;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
  }

  .Input {
    flex: 1 0 auto;
    padding: 0 10px;
    line-height: 1;
    color: var(--color-text-dark);
    border-bottom: 1.5px solid rgba(218, 218, 218, 0.4);
    height: 40px;
    caret-color: var(--color-hover-btn-dark);
  }
  .Input:focus {
    border-bottom: 1.5px solid var(--color-hover-btn-dark);
  }
  input:-webkit-autofill:active {
    transition: background-color 5000s ease-in-out 0s;
    -webkit-transition: background-color 9999s ease-out;
    -webkit-box-shadow: 0 0 0px 1000px white inset !important;
    -webkit-text-fill-color: #fff !important;
  }

  .Button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px;
    cursor: pointer;
    font-size: 18px;
    line-height: 1;
    font-weight: 500;
    width: 360px;
    height: 50px;
    font-family: "GilroyMedium";
    position: absolute;
    bottom: 0;
    left: 20px;
  }
  .Button.yellow {
    background-color: #41d8e6;
    color: var(--color-card-bg-dark);
  }
  .Button.yellow:hover {
    background-color: var(--color-hover-btn-dark);
  }
  .Button.yellow:active {
    bottom: -1px;
    left: 21px;
  }
`;

export default LoginPage;
