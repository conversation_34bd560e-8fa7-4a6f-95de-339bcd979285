import {
  AgeGroupOption,
  InspectionOption,
  RegisterOption,
  <PERSON>ati<PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>,
  Wrapper,
} from "@/components";
import { useStatistics } from "@/hooks";
import styled from "@emotion/styled";

const COMPONENT_MAP = {
  가입자: RegisterOption,
  "사용자(애완동물)": AgeGroupOption,
  검사: InspectionOption,
  탈퇴사유: Withdrawal,
};

export const StatisticPage = () => {
  const [filter] = useStatistics();

  const Component = COMPONENT_MAP[filter as keyof typeof COMPONENT_MAP];

  return (
    <Wrapper>
      <_Title>Statistics</_Title>
      <StatisticFilter />
      {Component && <Component />}
    </Wrapper>
  );
};

const _Title = styled.h2`
  font-size: 2rem;
  // margin: 1rem 0;
  font-family: "GilroyMedium";
  font-weight: 400;
  font-size: 34px;
  padding-bottom: 10px;
  color: var(--color-text-dark);
`;
