import { css } from "@emotion/react";

export const GlobalStyle = css`
  * {
    margin: 0;
    padding: 0;
    outline: 0;
    box-sizing: border-box;
    border: 0;
  }

  body {
    font-family: "Pretendard", "Noto Sans KR";
    color: var(--color-text-dark);
  }

  .en {
    font-family: "Gilroy";
  }

  a {
    text-decoration: none;
    color: inherit;

    &:hover {
      text-decoration: none;
      color: none;
    }

    &:active {
      text-decoration: none;
      color: black;
    }

    &:visited {
      text-decoration: none;
      color: black;
    }

    &:link {
      text-decoration: none;
      color: black;
    }
  }

  select,
  input,
  textarea {
    background-color: transparent;
    color: var(--color-grey-text-dark);
  }

  img {
    object-fit: contain;
  }

  :root {
    --color-bg-dark: #f6f6f6;
    --color-card-bg-dark: #ffffff;
    --color-nav-bg-dark: #ffffff;
    --color-text-dark: #000000;
    --color-grey-text-dark: #858585;
    --color-grey-hover-dark: #000000;
    --color-underline-dark: #dadada;
    --color-active-btn-dark: #41d8e6;
    --color-hover-btn-dark: rgba(65, 216, 230, 0.7);
    --color-submenu-bg-dark: #ffffff;
    --color-submenu-text-dark: #646464;
  }

  html.dark {
    --color-bg-dark: #0f172a;
    --color-card-bg-dark: #1e293c;
    --color-nav-bg-dark: #1e293c;
    --color-text-dark: #ffffff;
    --color-grey-text-dark: #94a3b8;
    --color-grey-hover-dark: #41d8e6;
    --color-underline-dark: #646464;
    --color-active-btn-dark: rgba(65, 216, 230, 0.6);
    --color-hover-btn-dark: rgba(65, 216, 230, 0.2);
    --color-submenu-bg-dark: #2c384c;
    --color-submenu-text-dark: #ffffff;
  }
`;
