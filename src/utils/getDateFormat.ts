const getTwoDigits = (num: number) => {
  return num < 10 ? `0${num}` : num;
};

export const getDateFormat = (d: string) => {
  const date = new Date(d);
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  const second = date.getSeconds();

  return `${year}-${getTwoDigits(month)}-${getTwoDigits(day)} ${getTwoDigits(
    hour
  )}:${getTwoDigits(minute)}:${getTwoDigits(second)}`;
};
