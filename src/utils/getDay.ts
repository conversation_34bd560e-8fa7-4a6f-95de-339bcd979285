const weekend = {
  Mon: "월",
  <PERSON><PERSON>: "화",
  Wed: "수",
  Thu: "목",
  Fri: "금",
  Sat: "토",
  Sun: "일",
};

export const getDay = (
  date: Date | null | [Date | null, Date | null]
): string => {
  const [year, month, day] = date?.toLocaleString().split(" ") as string[];
  const week = date?.toString().split(" ")[0];

  return `${year}-${month}-${day} (${
    weekend[week as keyof typeof weekend]
  }요일)`.replace(/\./g, "");
};
